# PCA9685舵机控制系统

基于TuyaOS的PCA9685 16通道PWM驱动器实现，用于控制MG90S舵机。

## 硬件连接

### PCA9685模块连接
| PCA9685引脚 | TuyaOS引脚 | 说明 |
|-------------|------------|------|
| VCC         | 5V         | 电源正极 |
| GND         | GND        | 电源负极 |
| SDA         | P06        | I2C数据线 |
| SCL         | P07        | I2C时钟线 |

### MG90S舵机连接
| 舵机引脚 | PCA9685接口 | 说明 |
|----------|-------------|------|
| 红线(VCC) | V+         | 电源正极 |
| 棕线(GND) | GND        | 电源负极 |
| 橙线(信号) | PWM0       | PWM信号线(最左边接口) |

## 软件架构

### 核心文件
```
apps/tuya.ai/your_chat_bot/
├── include/
│   ├── pca9685_driver.h        # PCA9685驱动头文件
│   ├── pca9685_servo_test.h    # 测试程序头文件
│   └── pwm_door_control.h      # 门控制头文件(已修改)
└── src/
    ├── pca9685_driver.c        # PCA9685驱动实现
    ├── pca9685_servo_test.c    # 测试程序实现
    ├── pwm_door_control.c      # 门控制实现(已修改)
    └── tuya_main.c             # 主程序(已修改)
```

### 主要功能模块

#### 1. PCA9685驱动 (`pca9685_driver.c`)
- I2C通信管理
- PWM频率设置(50Hz)
- 16通道PWM输出控制
- 舵机角度转换算法

#### 2. 舵机控制 (`pwm_door_control.c`)
- 基于PCA9685的舵机控制
- 门开关状态管理
- 角度精确控制(0-180度)

#### 3. 测试程序 (`pca9685_servo_test.c`)
- 基础功能测试
- 角度扫描测试
- 精度测试
- 门控制功能测试

## API接口

### PCA9685驱动接口

```c
// 初始化PCA9685驱动
OPERATE_RET pca9685_init(void);

// 设置舵机角度
OPERATE_RET pca9685_set_servo_angle(uint8_t channel, uint16_t angle);

// 设置舵机脉宽
OPERATE_RET pca9685_set_servo_pulse_width(uint8_t channel, uint16_t pulse_width);

// 停止指定通道
OPERATE_RET pca9685_stop_channel(uint8_t channel);

// 反初始化
OPERATE_RET pca9685_deinit(void);
```

### 门控制接口

```c
// 初始化门控制系统
OPERATE_RET pwm_door_control_init(void);

// 控制门开关
OPERATE_RET pwm_door_control_door(servo_id_e servo_id, bool open);

// 设置舵机角度
OPERATE_RET pwm_door_control_set_angle(servo_id_e servo_id, uint16_t angle);

// 清理系统
void pwm_door_control_cleanup(void);
```

### 测试接口

```c
// 启动测试
OPERATE_RET pca9685_servo_test_start(void);

// 停止测试
OPERATE_RET pca9685_servo_test_stop(void);
```

## 配置参数

### PCA9685配置
```c
#define PCA9685_I2C_ADDR        0x40    // I2C地址
#define PCA9685_PWM_FREQ        50      // PWM频率(Hz)
#define PCA9685_PWM_RESOLUTION  4096    // PWM分辨率(12位)
```

### MG90S舵机参数
```c
#define MG90S_MIN_PULSE_WIDTH   500     // 最小脉宽(μs) - 0度
#define MG90S_MAX_PULSE_WIDTH   2500    // 最大脉宽(μs) - 180度
#define MG90S_CENTER_PULSE_WIDTH 1500   // 中心脉宽(μs) - 90度
```

### I2C引脚配置
```c
#define PCA9685_I2C_SDA_PIN     TUYA_GPIO_NUM_6  // P06
#define PCA9685_I2C_SCL_PIN     TUYA_GPIO_NUM_7  // P07
```

## 使用示例

### 基本使用
```c
#include "pca9685_driver.h"

// 初始化
pca9685_init();

// 设置舵机到90度
pca9685_set_servo_angle(0, 90);

// 设置舵机到0度
pca9685_set_servo_angle(0, 0);

// 设置舵机到180度
pca9685_set_servo_angle(0, 180);

// 停止舵机
pca9685_stop_channel(0);

// 清理
pca9685_deinit();
```

### 门控制使用
```c
#include "pwm_door_control.h"

// 初始化门控制系统
pwm_door_control_init();

// 开门
pwm_door_control_door(SERVO_ID_DOOR_1, true);

// 关门
pwm_door_control_door(SERVO_ID_DOOR_1, false);

// 设置自定义角度
pwm_door_control_set_angle(SERVO_ID_DOOR_1, 45);

// 清理
pwm_door_control_cleanup();
```

## 测试功能

系统启动后会自动运行测试程序，包括：

1. **基础功能测试**: 验证PCA9685设备状态和基本角度设置
2. **角度扫描测试**: 0-180度角度扫描，验证舵机响应
3. **精度测试**: 测试特定角度的精确控制
4. **门控制测试**: 测试开门/关门功能

### 测试日志示例
```
🧪 启动PCA9685舵机控制测试...
✅ PCA9685测试任务启动成功
🔧 初始化PCA9685驱动...
✅ PCA9685驱动初始化完成
📊 PCA9685配置:
   - I2C地址: 0x40
   - I2C端口: 0
   - PWM频率: 50 Hz
   - SDA引脚: P06
   - SCL引脚: P07
🎛️ 设置舵机通道0角度: 90° → 1500μs
```

## 故障排除

### 常见问题

1. **舵机不响应**
   - 检查电源连接(5V/GND)
   - 检查I2C连接(SDA/SCL)
   - 确认PCA9685地址(0x40)

2. **舵机抖动**
   - 检查电源稳定性
   - 调整脉宽参数
   - 确认PWM频率(50Hz)

3. **I2C通信失败**
   - 检查引脚配置
   - 确认I2C速度(100kHz)
   - 检查上拉电阻

### 调试方法

1. **启用调试日志**
   ```c
   tal_log_init(TAL_LOG_LEVEL_DEBUG, 1024, (TAL_LOG_OUTPUT_CB)tkl_log_output);
   ```

2. **检查设备状态**
   ```c
   const pca9685_device_t *device = pca9685_get_device_status();
   if (device && device->initialized) {
       PR_INFO("PCA9685设备正常");
   }
   ```

3. **测试I2C通信**
   - 使用示波器检查I2C信号
   - 确认设备响应ACK

## 参考资料

- [TuyaOS PWM驱动文档](https://developer.tuya.com/cn/docs/iot-device-dev/TuyaOS-iot_abi_driver_pwm?id=Kcusum077ezvn)
- [PCA9685数据手册](https://www.nxp.com/docs/en/data-sheet/PCA9685.pdf)
- [MG90S舵机规格](http://www.ee.ic.ac.uk/pcheung/teaching/DE1_EE/stores/sg90_datasheet.pdf)
- [参考实现](https://gitee.com/qi-zezhong/pca9685-stm32)

## 版本历史

- v1.0.0: 初始版本，支持基本PCA9685驱动和MG90S舵机控制
- 支持TuyaOS I2C接口
- 支持16通道PWM输出
- 支持角度精确控制(0-180度)
- 包含完整测试程序
