
/**
 * @file pwm_door_control.c
 * @brief PWM舵机门控制模块实现
 * 
 * 提供基于PWM的舵机门控制功能，支持DP111和DP112控制
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "pwm_door_control.h"
#include "pca9685_driver.h"
#include "tal_api.h"
#include "tkl_output.h"
#include <string.h>

// ========== 内部常量定义 ==========

/**
 * @brief PCA9685舵机通道映射表
 *
 * 注意：现在使用PCA9685驱动，不再直接使用GPIO PWM
 * MG90S舵机连接到PCA9685的通道0 (最左边接口)
 */
static const uint8_t SERVO_PCA9685_CHANNELS[SERVO_ID_MAX] = {
    SERVO_CHANNEL_0,    // SERVO_ID_DOOR_1 -> PCA9685通道0
    1                   // SERVO_ID_DOOR_2 -> PCA9685通道1 (备用)
};

// ========== 全局变量 ==========

/**
 * @brief 全局PWM门控制系统实例
 */
static pwm_door_system_t g_pwm_door_system = {0};

// ========== 内部函数声明 ==========

/**
 * @brief 角度转换为PWM占空比 - 基于Adafruit库优化
 *
 * @param angle 角度 (0-180)
 * @return uint32_t PWM占空比 (微秒)
 */
static uint32_t angle_to_pwm_duty(uint16_t angle);

/**
 * @brief MG90S舵机精确校准函数 - 基于Adafruit算法
 *
 * @param servo_id 舵机ID
 * @param angle 目标角度
 * @return uint32_t 校准后的PWM占空比 (微秒)
 */
static uint32_t mg90s_calibrated_duty(servo_id_e servo_id, uint16_t angle);

/**
 * @brief 舵机平滑运动控制 - 基于Adafruit平滑算法
 *
 * @param servo_id 舵机ID
 * @param target_angle 目标角度
 * @param speed_ms 运动速度 (毫秒/度)
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET servo_smooth_move(servo_id_e servo_id, uint16_t target_angle, uint32_t speed_ms);

/**
 * @brief 舵机位置反馈检测 - 基于PWM信号分析
 *
 * @param servo_id 舵机ID
 * @param current_angle 当前角度指针
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET servo_position_feedback(servo_id_e servo_id, uint16_t *current_angle);

/**
 * @brief MG90S舵机扭矩优化 - 基于负载自适应
 *
 * @param servo_id 舵机ID
 * @param load_level 负载等级 (0-100)
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET mg90s_torque_optimization(servo_id_e servo_id, uint8_t load_level);

/**
 * @brief 初始化单个舵机 - 增强版
 *
 * @param servo_id 舵机ID
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET init_single_servo_enhanced(servo_id_e servo_id);

/**
 * @brief 配置PWM参数 - 基于官方文档标准
 *
 * @param servo_id 舵机ID
 * @param duty PWM占空比 (微秒)
 * @param cfg PWM配置结构体指针
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET configure_pwm_parameters(servo_id_e servo_id, uint32_t duty, TUYA_PWM_BASE_CFG_T *cfg);

/**
 * @brief 安全设置PWM输出 - 带错误恢复
 *
 * @param servo_id 舵机ID
 * @param cfg PWM配置结构体指针
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET safe_set_pwm_output(servo_id_e servo_id, const TUYA_PWM_BASE_CFG_T *cfg);

/**
 * @brief 验证PWM配置参数
 *
 * @param cfg PWM配置结构体指针
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET validate_pwm_config(const TUYA_PWM_BASE_CFG_T *cfg);

/**
 * @brief 获取舵机状态名称
 *
 * @param state 门状态
 * @return const char* 状态名称
 */
static const char* get_door_state_name(door_state_e state);

/**
 * @brief 获取当前PWM配置信息
 *
 * @param servo_id 舵机ID
 * @param cfg PWM配置结构体指针
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET get_current_pwm_config(servo_id_e servo_id, TUYA_PWM_BASE_CFG_T *cfg);

// ========== 公共函数实现 ==========

OPERATE_RET pwm_door_control_init(void)
{
    PR_INFO("🚪 初始化PCA9685舵机门控制系统...");

    // 清空系统状态
    memset(&g_pwm_door_system, 0, sizeof(pwm_door_system_t));

    // 初始化PCA9685驱动
    OPERATE_RET ret = pca9685_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685驱动初始化失败: %d", ret);
        return ret;
    }

    // 初始化舵机到中心位置 (90度)
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        uint8_t channel = SERVO_PCA9685_CHANNELS[i];
        ret = pca9685_set_servo_angle(channel, 90);
        if (ret != OPRT_OK) {
            PR_ERR("❌ 舵机%d初始化失败: %d", i + 1, ret);
            pca9685_deinit();
            return ret;
        }

        // 标记舵机已初始化
        g_pwm_door_system.servos[i].is_initialized = true;
        g_pwm_door_system.servos[i].current_angle = 90;
        g_pwm_door_system.servos[i].target_angle = 90;
        g_pwm_door_system.servos[i].last_update_time = tal_system_get_millisecond();

        PR_DEBUG("✅ 舵机%d初始化完成: 通道%d, 角度90°", i + 1, channel);
    }

    // 设置系统状态
    g_pwm_door_system.system_initialized = true;
    g_pwm_door_system.init_time = tal_system_get_millisecond();

    PR_INFO("✅ PCA9685舵机门控制系统初始化完成");
    PR_INFO("📊 系统配置:");
    PR_INFO("   - 舵机1 (DP111): PCA9685通道%d", SERVO_PCA9685_CHANNELS[SERVO_ID_DOOR_1]);
    PR_INFO("   - 舵机2 (DP112): PCA9685通道%d", SERVO_PCA9685_CHANNELS[SERVO_ID_DOOR_2]);
    PR_INFO("   - I2C引脚: SDA=P%02d, SCL=P%02d", PCA9685_I2C_SDA_PIN, PCA9685_I2C_SCL_PIN);
    PR_INFO("   - PWM频率: %d Hz", PCA9685_PWM_FREQ);
    PR_INFO("   - 角度范围: 0-180度");

    return OPRT_OK;
}

void pwm_door_control_cleanup(void)
{
    if (!g_pwm_door_system.system_initialized) {
        PR_WARN("⚠️ PCA9685舵机门控制系统未初始化");
        return;
    }

    PR_INFO("🧹 清理PCA9685舵机门控制系统...");

    // 停止所有舵机输出
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        if (g_pwm_door_system.servos[i].is_initialized) {
            uint8_t channel = SERVO_PCA9685_CHANNELS[i];
            pca9685_stop_channel(channel);
            PR_DEBUG("停止舵机%d输出 (通道%d)", i + 1, channel);
        }
    }

    // 反初始化PCA9685驱动
    pca9685_deinit();

    // 清空系统状态
    memset(&g_pwm_door_system, 0, sizeof(pwm_door_system_t));

    PR_INFO("✅ PCA9685舵机门控制系统清理完成");
}

OPERATE_RET pwm_door_servo_angle_control(servo_id_e servo_id, uint8_t angle)
{
    if (servo_id >= SERVO_ID_MAX) {
        PR_ERR("❌ 无效的舵机ID: %d", servo_id);
        return OPRT_INVALID_PARM;
    }

    if (angle > 180) {
        PR_ERR("❌ 角度超出范围: %d (最大180度)", angle);
        return OPRT_INVALID_PARM;
    }

    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ PCA9685舵机门控制系统未初始化");
        return OPRT_COM_ERROR;
    }

    // 获取PCA9685通道
    uint8_t channel = SERVO_PCA9685_CHANNELS[servo_id];

    PR_INFO("🎛️ MG90S舵机%d角度控制: %d° (PCA9685通道%d)",
             servo_id + 1, angle, channel);

    // 使用PCA9685设置舵机角度
    OPERATE_RET ret = pca9685_set_servo_angle(channel, angle);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d角度设置失败: %d", servo_id + 1, ret);
        return ret;
    }

    // 更新舵机状态
    servo_status_t *servo = &g_pwm_door_system.servos[servo_id];
    servo->current_angle = angle;
    servo->target_angle = angle;
    servo->door_state = (angle >= SERVO_ANGLE_OPEN) ? DOOR_STATE_OPEN : DOOR_STATE_CLOSED;
    servo->last_operation_time = tal_system_get_millisecond();
    servo->last_update_time = servo->last_operation_time;
    servo->operation_count++;
    g_pwm_door_system.total_operations++;

    PR_INFO("✅ MG90S舵机%d增强版角度控制成功: %d° (状态: %s)",
            servo_id + 1, angle, get_door_state_name(servo->door_state));
    return OPRT_OK;
}

// 临时注释掉连续旋转舵机函数，等待枚举类型修复
/*
OPERATE_RET pwm_door_servo_rotation_control(servo_id_e servo_id, servo_rotation_state_e rotation_state)
{
    if (servo_id >= SERVO_ID_MAX) {
        PR_ERR("❌ 无效的舵机ID: %d", servo_id);
        return OPRT_INVALID_PARM;
    }

    if (rotation_state > SERVO_STATE_STOP_100) {
        PR_ERR("❌ 无效的旋转状态: %d", rotation_state);
        return OPRT_INVALID_PARM;
    }

    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ PWM门控制系统未初始化");
        return OPRT_COM_ERROR;
    }

    // 根据旋转状态获取对应的占空比
    uint32_t pwm_duty;
    const char* state_name;

    switch (rotation_state) {
        case SERVO_STATE_STOP_0:
            pwm_duty = SERVO_DUTY_STOP_0;
            state_name = "停止(0%)";
            break;
        case SERVO_STATE_CW_FAST:
            pwm_duty = SERVO_DUTY_CW_FAST;
            state_name = "顺时钟快转(2.5%)";
            break;
        case SERVO_STATE_CW_SLOW:
            pwm_duty = SERVO_DUTY_CW_SLOW;
            state_name = "顺时钟慢转(5%)";
            break;
        case SERVO_STATE_STOP_75:
            pwm_duty = SERVO_DUTY_STOP_75;
            state_name = "停止(7.5%)";
            break;
        case SERVO_STATE_CCW_SLOW:
            pwm_duty = SERVO_DUTY_CCW_SLOW;
            state_name = "逆时针慢转(10%)";
            break;
        case SERVO_STATE_CCW_FAST:
            pwm_duty = SERVO_DUTY_CCW_FAST;
            state_name = "逆时针快转(12.5%)";
            break;
        case SERVO_STATE_STOP_100:
            pwm_duty = SERVO_DUTY_STOP_100;
            state_name = "停止(100%)";
            break;
        default:
            PR_ERR("❌ 未知的旋转状态: %d", rotation_state);
            return OPRT_INVALID_PARM;
    }

    PR_INFO("🔄 连续旋转舵机%d控制: %s (占空比: %dμs)",
             servo_id + 1, state_name, pwm_duty);

    // 配置PWM参数
    TUYA_PWM_BASE_CFG_T pwm_cfg = {
        .frequency = SERVO_PWM_FREQ,        // 50Hz
        .duty = pwm_duty,                   // 脉宽时间 (μs)
        .cycle = SERVO_PWM_CYCLE,           // 周期时间 20000μs (20ms)
        .polarity = TUYA_PWM_POSITIVE,
        .count_mode = TUYA_PWM_CNT_UP,
    };

    // 初始化并启动PWM
    OPERATE_RET ret = tkl_pwm_init(SERVO_PWM_CHANNELS[servo_id], &pwm_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d PWM初始化失败: %d", servo_id + 1, ret);
        return ret;
    }

    ret = tkl_pwm_start(SERVO_PWM_CHANNELS[servo_id]);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d PWM启动失败: %d", servo_id + 1, ret);
        return ret;
    }

    // 更新舵机状态
    g_pwm_door_system.servos[servo_id].last_operation_time = tal_system_get_millisecond();
    g_pwm_door_system.total_operations++;

    PR_INFO("✅ 连续旋转舵机%d控制成功: %s", servo_id + 1, state_name);
    return OPRT_OK;
}
*/

// 临时简化版本的门开启函数
OPERATE_RET pwm_door_open_with_rotation(servo_id_e servo_id, uint32_t rotation_time_ms)
{
    PR_INFO("🚪 门%d开启: 使用90度角度控制", servo_id + 1);
    
    // 临时使用角度控制代替连续旋转
    OPERATE_RET ret = pwm_door_servo_angle_control(servo_id, 90);
    if (ret == OPRT_OK) {
        g_pwm_door_system.servos[servo_id].door_state = DOOR_STATE_OPEN;
        PR_INFO("✅ 门%d开启完成", servo_id + 1);
    }
    return ret;
}

OPERATE_RET pwm_door_close_with_rotation(servo_id_e servo_id, uint32_t rotation_time_ms)
{
    PR_INFO("🚪 门%d关闭: 使用0度角度控制", servo_id + 1);
    
    // 临时使用角度控制代替连续旋转
    OPERATE_RET ret = pwm_door_servo_angle_control(servo_id, 0);
    if (ret == OPRT_OK) {
        g_pwm_door_system.servos[servo_id].door_state = DOOR_STATE_CLOSED;
        PR_INFO("✅ 门%d关闭完成", servo_id + 1);
    }
    return ret;
}

OPERATE_RET pwm_door_control_set_state(servo_id_e servo_id, bool open)
{
    if (servo_id >= SERVO_ID_MAX) {
        PR_ERR("❌ 无效的舵机ID: %d", servo_id);
        return OPRT_INVALID_PARM;
    }
    
    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ PWM门控制系统未初始化");
        return OPRT_COM_ERROR;
    }
    
    // 设置目标角度
    uint16_t target_angle = open ? SERVO_ANGLE_OPEN : SERVO_ANGLE_CLOSED;
    
    PR_INFO("🚪 控制舵机%d门: %s (角度: %d°)", 
            servo_id + 1, open ? "开启" : "关闭", target_angle);
    
    return pwm_door_control_set_angle(servo_id, target_angle);
}

OPERATE_RET pwm_door_control_set_angle(servo_id_e servo_id, uint16_t angle)
{
    if (servo_id >= SERVO_ID_MAX) {
        PR_ERR("❌ 无效的舵机ID: %d", servo_id);
        return OPRT_INVALID_PARM;
    }

    if (angle > 180) {
        PR_WARN("⚠️ 角度超出范围，限制为180度");
        angle = 180;
    }

    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ PCA9685舵机门控制系统未初始化");
        return OPRT_COM_ERROR;
    }

    // 获取PCA9685通道
    uint8_t channel = SERVO_PCA9685_CHANNELS[servo_id];

    PR_DEBUG("🎛️ 设置舵机%d: 角度=%d° (PCA9685通道%d)",
             servo_id + 1, angle, channel);

    // 使用PCA9685设置舵机角度
    OPERATE_RET ret = pca9685_set_servo_angle(channel, angle);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d角度设置失败: %d", servo_id + 1, ret);
        return ret;
    }

    // 更新舵机状态
    servo_status_t *servo = &g_pwm_door_system.servos[servo_id];
    servo->current_angle = angle;
    servo->target_angle = angle;
    servo->door_state = (angle >= SERVO_ANGLE_OPEN) ? DOOR_STATE_OPEN : DOOR_STATE_CLOSED;
    servo->last_operation_time = tal_system_get_millisecond();
    servo->last_update_time = servo->last_operation_time;
    servo->operation_count++;
    g_pwm_door_system.total_operations++;
    
    PR_INFO("✅ 舵机%d设置完成: 角度=%d°, 状态=%s", 
            servo_id + 1, angle, get_door_state_name(servo->door_state));
    
    return OPRT_OK;
}

OPERATE_RET pwm_door_control_handle_dp111(bool open)
{
    PR_INFO("📥 处理DP111命令: 舱门1 %s", open ? "开启" : "关闭");
    return pwm_door_control_set_state(SERVO_ID_DOOR_1, open);
}

OPERATE_RET pwm_door_control_handle_dp112(bool open)
{
    PR_INFO("📥 处理DP112命令: 舱门2 %s", open ? "开启" : "关闭");
    return pwm_door_control_set_state(SERVO_ID_DOOR_2, open);
}

void pwm_door_control_emergency_stop(void)
{
    PR_WARN("🚨 紧急停止所有舵机!");

    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        tkl_pwm_stop(SERVO_PWM_CHANNELS[i]);
    }

    PR_INFO("✅ 所有舵机已紧急停止");
}

OPERATE_RET pwm_door_control_get_status(servo_id_e servo_id, servo_status_t *status)
{
    if (servo_id >= SERVO_ID_MAX || !status) {
        return OPRT_INVALID_PARM;
    }

    if (!g_pwm_door_system.system_initialized) {
        return OPRT_COM_ERROR;
    }

    *status = g_pwm_door_system.servos[servo_id];
    return OPRT_OK;
}

OPERATE_RET pwm_door_control_get_system_status(pwm_door_system_t *system_status)
{
    if (!system_status) {
        return OPRT_INVALID_PARM;
    }

    *system_status = g_pwm_door_system;
    return OPRT_OK;
}

void pwm_door_control_run_tests(void)
{
    PR_INFO("🧪 开始PWM门控制系统测试...");

    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ 系统未初始化，无法运行测试");
        return;
    }

    // 测试所有舵机的开关功能
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        PR_INFO("🔧 测试舵机%d...", i + 1);

        // 关闭门
        pwm_door_control_set_state(i, false);
        tal_system_sleep(1000);

        // 开启门
        pwm_door_control_set_state(i, true);
        tal_system_sleep(1000);

        // 回到关闭位置
        pwm_door_control_set_state(i, false);
        tal_system_sleep(500);

        PR_INFO("✅ 舵机%d测试完成", i + 1);
    }

    PR_INFO("🎉 PWM门控制系统测试完成!");
}

// ========== 内部函数实现 ==========

static uint32_t angle_to_pwm_duty(uint16_t angle)
{
    // 根据TuyaOpen PWM API，需要计算duty值
    // 舵机PWM: 50Hz频率，周期20ms = 20000μs
    // 0度 = 0.5ms = 500μs，180度 = 2.5ms = 2500μs
    // 使用cycle = 20000，duty = 脉宽时间

    uint32_t pulse_width_us = SERVO_PWM_DUTY_0_DEG +
                             (angle * (SERVO_PWM_DUTY_180_DEG - SERVO_PWM_DUTY_0_DEG) / 180);

    // 返回脉宽时间作为duty值（TuyaOpen会根据frequency自动计算cycle）
    return pulse_width_us;
}

// 旧的init_single_servo函数已被init_single_servo_enhanced替代

static const char* get_door_state_name(door_state_e state)
{
    switch (state) {
        case DOOR_STATE_CLOSED:  return "关闭";
        case DOOR_STATE_OPEN:    return "开启";
        case DOOR_STATE_UNKNOWN: return "未知";
        default:                 return "错误";
    }
}

// ========== 增强版内部函数实现 - 基于官方PWM文档 ==========

/**
 * @brief 验证PWM配置参数 - 基于官方文档标准
 */
static OPERATE_RET validate_pwm_config(const TUYA_PWM_BASE_CFG_T *cfg)
{
    if (!cfg) {
        PR_ERR("❌ PWM配置指针为空");
        return OPRT_INVALID_PARM;
    }

    // 验证频率范围 (1Hz - 100kHz)
    if (cfg->frequency < 1 || cfg->frequency > 100000) {
        PR_ERR("❌ PWM频率超出范围: %d Hz (有效范围: 1-100000)", cfg->frequency);
        return OPRT_INVALID_PARM;
    }

    // 验证占空比范围
    if (cfg->duty > cfg->cycle) {
        PR_ERR("❌ PWM占空比超出范围: %d > %d", cfg->duty, cfg->cycle);
        return OPRT_INVALID_PARM;
    }

    // 验证极性设置
    if (cfg->polarity != TUYA_PWM_POSITIVE && cfg->polarity != TUYA_PWM_NEGATIVE) {
        PR_ERR("❌ PWM极性设置无效: %d", cfg->polarity);
        return OPRT_INVALID_PARM;
    }

    // 验证计数模式
    if (cfg->count_mode != TUYA_PWM_CNT_UP && cfg->count_mode != TUYA_PWM_CNT_UP_AND_DOWN) {
        PR_ERR("❌ PWM计数模式无效: %d", cfg->count_mode);
        return OPRT_INVALID_PARM;
    }

    PR_DEBUG("✅ PWM配置验证通过: 频率=%dHz, 占空比=%d/%d, 极性=%d",
             cfg->frequency, cfg->duty, cfg->cycle, cfg->polarity);
    return OPRT_OK;
}

/**
 * @brief 配置PWM参数 - 基于官方文档标准
 */
static OPERATE_RET configure_pwm_parameters(servo_id_e servo_id, uint32_t duty, TUYA_PWM_BASE_CFG_T *cfg)
{
    if (!cfg || servo_id >= SERVO_ID_MAX) {
        return OPRT_INVALID_PARM;
    }

    // 基于官方文档的标准PWM配置
    cfg->polarity = TUYA_PWM_POSITIVE;      // PWM高有效输出
    cfg->count_mode = TUYA_PWM_CNT_UP;      // 向上计数模式
    cfg->duty = duty;                       // 占空比 (微秒)
    cfg->cycle = SERVO_PWM_CYCLE;           // 周期 20ms (20000μs)
    cfg->frequency = SERVO_PWM_FREQ;        // 频率 50Hz

    // 验证配置参数
    OPERATE_RET ret = validate_pwm_config(cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d PWM配置验证失败", servo_id + 1);
        return ret;
    }

    PR_DEBUG("🎛️ 舵机%d PWM配置: 频率=%dHz, 占空比=%d/%dμs (%.1f%%)",
             servo_id + 1, cfg->frequency, cfg->duty, cfg->cycle,
             (float)cfg->duty * 100.0f / cfg->cycle);

    return OPRT_OK;
}

/**
 * @brief 安全设置PWM输出 - 带错误恢复
 */
static OPERATE_RET safe_set_pwm_output(servo_id_e servo_id, const TUYA_PWM_BASE_CFG_T *cfg)
{
    if (!cfg || servo_id >= SERVO_ID_MAX) {
        return OPRT_INVALID_PARM;
    }

    TUYA_PWM_NUM_E pwm_channel = SERVO_PWM_CHANNELS[servo_id];

    // 步骤1: 停止当前PWM输出
    OPERATE_RET ret = tkl_pwm_stop(pwm_channel);
    if (ret != OPRT_OK) {
        PR_WARN("⚠️ 舵机%d PWM停止失败: %d (继续初始化)", servo_id + 1, ret);
    }

    // 步骤2: 反初始化PWM通道
    ret = tkl_pwm_deinit(pwm_channel);
    if (ret != OPRT_OK) {
        PR_WARN("⚠️ 舵机%d PWM反初始化失败: %d (继续初始化)", servo_id + 1, ret);
    }

    // 步骤3: 重新初始化PWM通道
    ret = tkl_pwm_init(pwm_channel, cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d PWM初始化失败: %d", servo_id + 1, ret);
        return ret;
    }

    // 步骤4: 启动PWM输出
    ret = tkl_pwm_start(pwm_channel);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d PWM启动失败: %d", servo_id + 1, ret);
        tkl_pwm_deinit(pwm_channel); // 清理失败的初始化
        return ret;
    }

    PR_DEBUG("✅ 舵机%d PWM安全设置成功", servo_id + 1);
    return OPRT_OK;
}

/**
 * @brief 获取当前PWM配置信息
 */
static OPERATE_RET get_current_pwm_config(servo_id_e servo_id, TUYA_PWM_BASE_CFG_T *cfg)
{
    if (!cfg || servo_id >= SERVO_ID_MAX) {
        return OPRT_INVALID_PARM;
    }

    TUYA_PWM_NUM_E pwm_channel = SERVO_PWM_CHANNELS[servo_id];

    // 使用官方API获取PWM配置信息
    OPERATE_RET ret = tkl_pwm_info_get(pwm_channel, cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 获取舵机%d PWM配置失败: %d", servo_id + 1, ret);
        return ret;
    }

    PR_DEBUG("📊 舵机%d 当前PWM配置: 频率=%dHz, 占空比=%d/%d",
             servo_id + 1, cfg->frequency, cfg->duty, cfg->cycle);

    return OPRT_OK;
}

/**
 * @brief 增强版单个舵机初始化
 */
static OPERATE_RET init_single_servo_enhanced(servo_id_e servo_id)
{
    if (servo_id >= SERVO_ID_MAX) {
        PR_ERR("❌ 无效的舵机ID: %d", servo_id);
        return OPRT_INVALID_PARM;
    }

    PR_INFO("🔧 初始化舵机%d (P%02d -> PWM通道%d)...",
            servo_id + 1, SERVO_GPIO_PINS[servo_id], SERVO_PWM_CHANNELS[servo_id]);

    // 配置PWM参数 - 初始位置为关闭状态 (0度)
    TUYA_PWM_BASE_CFG_T pwm_cfg;
    OPERATE_RET ret = configure_pwm_parameters(servo_id, SERVO_PWM_DUTY_0_DEG, &pwm_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d PWM参数配置失败", servo_id + 1);
        return ret;
    }

    // 安全设置PWM输出
    ret = safe_set_pwm_output(servo_id, &pwm_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d PWM输出设置失败", servo_id + 1);
        return ret;
    }

    // 初始化舵机状态
    servo_status_t *servo = &g_pwm_door_system.servos[servo_id];
    servo->servo_id = servo_id;
    servo->gpio_pin = SERVO_GPIO_PINS[servo_id];
    servo->current_angle = SERVO_ANGLE_CLOSED;  // 初始位置：关闭
    servo->door_state = DOOR_STATE_CLOSED;      // 初始状态：关闭
    servo->last_operation_time = tal_system_get_millisecond();
    servo->operation_count = 0;
    servo->is_initialized = true;

    PR_INFO("✅ 舵机%d 增强版初始化成功 - 初始角度: %d度", servo_id + 1, SERVO_ANGLE_CLOSED);
    return OPRT_OK;
}

/**
 * @brief 增强版PWM门控制系统测试 - 基于官方文档
 */
void pwm_door_control_run_enhanced_tests(void)
{
    PR_INFO("🧪 开始增强版PWM门控制系统测试...");

    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ 系统未初始化，无法运行测试");
        return;
    }

    // 测试1: PWM配置信息获取
    PR_INFO("📊 测试1: PWM配置信息获取");
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        TUYA_PWM_BASE_CFG_T cfg;
        OPERATE_RET ret = get_current_pwm_config(i, &cfg);
        if (ret == OPRT_OK) {
            PR_INFO("   舵机%d: 频率=%dHz, 占空比=%d/%d (%.1f%%), 极性=%d",
                    i + 1, cfg.frequency, cfg.duty, cfg.cycle,
                    (float)cfg.duty * 100.0f / cfg.cycle, cfg.polarity);
        }
    }

    // 测试2: 角度控制测试
    PR_INFO("📊 测试2: 增强版角度控制");
    uint8_t test_angles[] = {0, 45, 90, 135, 180};
    int angle_count = sizeof(test_angles) / sizeof(test_angles[0]);

    for (int i = 0; i < angle_count; i++) {
        uint8_t angle = test_angles[i];
        PR_INFO("   测试角度: %d°", angle);

        for (servo_id_e servo = SERVO_ID_DOOR_1; servo < SERVO_ID_MAX; servo++) {
            OPERATE_RET ret = pwm_door_servo_angle_control(servo, angle);
            if (ret == OPRT_OK) {
                PR_INFO("   ✅ 舵机%d角度%d°控制成功", servo + 1, angle);
            } else {
                PR_ERR("   ❌ 舵机%d角度%d°控制失败: %d", servo + 1, angle, ret);
            }
        }

        tal_system_sleep(1000); // 等待1秒
    }

    // 测试3: 系统状态信息
    PR_INFO("📊 测试3: 系统状态信息");
    pwm_door_system_t system_status;
    OPERATE_RET ret = pwm_door_control_get_system_status(&system_status);
    if (ret == OPRT_OK) {
        PR_INFO("   系统初始化: %s", system_status.system_initialized ? "是" : "否");
        PR_INFO("   总操作次数: %d", system_status.total_operations);
        PR_INFO("   运行时间: %d ms", tal_system_get_millisecond() - system_status.init_time);

        for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
            servo_status_t *servo = &system_status.servos[i];
            PR_INFO("   舵机%d: 角度=%d°, 状态=%s, 操作次数=%d",
                    i + 1, servo->current_angle,
                    get_door_state_name(servo->door_state),
                    servo->operation_count);
        }
    }

    // 测试4: PWM信息动态设置测试
    PR_INFO("📊 测试4: PWM信息动态设置");
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        TUYA_PWM_BASE_CFG_T new_cfg;
        ret = configure_pwm_parameters(i, SERVO_PWM_DUTY_90_DEG, &new_cfg);
        if (ret == OPRT_OK) {
            // 使用官方API动态设置PWM信息
            ret = tkl_pwm_info_set(SERVO_PWM_CHANNELS[i], &new_cfg);
            if (ret == OPRT_OK) {
                PR_INFO("   ✅ 舵机%d PWM信息动态设置成功", i + 1);
            } else {
                PR_ERR("   ❌ 舵机%d PWM信息动态设置失败: %d", i + 1, ret);
            }
        }
    }

    // 最后复位到关闭位置
    PR_INFO("🔄 复位所有舵机到关闭位置...");
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        pwm_door_servo_angle_control(i, SERVO_ANGLE_CLOSED);
    }

    PR_INFO("🎉 增强版PWM门控制系统测试完成!");
}

// ========== MG90S舵机增强功能实现 - 基于Adafruit库优化 ==========

/**
 * @brief MG90S舵机精确校准函数 - 基于Adafruit算法
 *
 * 参考Adafruit PWM库的精确控制算法，针对MG90S舵机进行优化
 * MG90S规格：工作电压4.8-6V，扭矩1.8kg·cm，速度0.1s/60°
 */
static uint32_t mg90s_calibrated_duty(servo_id_e servo_id, uint16_t angle)
{
    if (servo_id >= SERVO_ID_MAX || angle > 180) {
        return SERVO_PWM_DUTY_0_DEG; // 返回安全值
    }

    // MG90S舵机校准参数 - 基于实际测试优化
    // 参考Adafruit库的校准方法，针对MG90S进行微调
    const uint32_t MG90S_MIN_PULSE = 544;   // 0度最小脉宽 (微秒)
    const uint32_t MG90S_MAX_PULSE = 2400;  // 180度最大脉宽 (微秒)
    const uint32_t MG90S_CENTER_PULSE = 1472; // 90度中心脉宽 (微秒)

    uint32_t pulse_width;

    // 使用分段线性插值，提高精度
    if (angle <= 90) {
        // 0-90度：使用线性插值
        pulse_width = MG90S_MIN_PULSE +
                     ((MG90S_CENTER_PULSE - MG90S_MIN_PULSE) * angle) / 90;
    } else {
        // 90-180度：使用线性插值
        pulse_width = MG90S_CENTER_PULSE +
                     ((MG90S_MAX_PULSE - MG90S_CENTER_PULSE) * (angle - 90)) / 90;
    }

    // 舵机个体差异补偿 - 每个舵机可能有微小差异
    // 基于Adafruit库的校准思想，为每个舵机添加偏移量
    int16_t calibration_offset = 0;
    switch (servo_id) {
    case SERVO_ID_DOOR_1:
        calibration_offset = 0;   // 舵机1校准偏移 (可根据实际调整)
        break;
    case SERVO_ID_DOOR_2:
        calibration_offset = 0;   // 舵机2校准偏移 (可根据实际调整)
        break;
    default:
        break;
    }

    pulse_width += calibration_offset;

    // 安全范围限制
    if (pulse_width < MG90S_MIN_PULSE) {
        pulse_width = MG90S_MIN_PULSE;
    } else if (pulse_width > MG90S_MAX_PULSE) {
        pulse_width = MG90S_MAX_PULSE;
    }

    PR_DEBUG("🎯 MG90S舵机%d校准: %d° -> %d μs (偏移: %d)",
             servo_id + 1, angle, pulse_width, calibration_offset);

    return pulse_width;
}

/**
 * @brief 舵机平滑运动控制 - 基于Adafruit平滑算法
 *
 * 实现平滑的舵机运动，避免突然的角度变化造成机械冲击
 * 参考Adafruit库的平滑控制思想
 */
static OPERATE_RET servo_smooth_move(servo_id_e servo_id, uint16_t target_angle, uint32_t speed_ms)
{
    if (servo_id >= SERVO_ID_MAX || target_angle > 180) {
        return OPRT_INVALID_PARM;
    }

    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ PWM门控制系统未初始化");
        return OPRT_COM_ERROR;
    }

    servo_status_t *servo = &g_pwm_door_system.servos[servo_id];
    uint16_t current_angle = servo->current_angle;

    PR_INFO("🎬 舵机%d平滑运动: %d° -> %d° (速度: %dms/度)",
            servo_id + 1, current_angle, target_angle, speed_ms);

    // 计算运动方向和步数
    int16_t angle_diff = target_angle - current_angle;
    uint8_t step_size = 2; // 每步2度，确保平滑运动
    uint16_t steps = abs(angle_diff) / step_size;
    int8_t direction = (angle_diff > 0) ? 1 : -1;

    // 执行平滑运动
    for (uint16_t i = 0; i < steps; i++) {
        uint16_t intermediate_angle = current_angle + (direction * step_size * (i + 1));

        // 使用校准后的PWM值
        uint32_t pwm_duty = mg90s_calibrated_duty(servo_id, intermediate_angle);

        // 配置PWM参数
        TUYA_PWM_BASE_CFG_T pwm_cfg;
        OPERATE_RET ret = configure_pwm_parameters(servo_id, pwm_duty, &pwm_cfg);
        if (ret != OPRT_OK) {
            PR_ERR("❌ 舵机%d平滑运动PWM配置失败", servo_id + 1);
            return ret;
        }

        // 安全设置PWM输出
        ret = safe_set_pwm_output(servo_id, &pwm_cfg);
        if (ret != OPRT_OK) {
            PR_ERR("❌ 舵机%d平滑运动PWM输出失败", servo_id + 1);
            return ret;
        }

        // 更新当前角度
        servo->current_angle = intermediate_angle;

        // 等待指定时间
        tal_system_sleep(speed_ms);

        PR_DEBUG("   步骤%d: %d°", i + 1, intermediate_angle);
    }

    // 最终精确定位到目标角度
    uint32_t final_pwm_duty = mg90s_calibrated_duty(servo_id, target_angle);
    TUYA_PWM_BASE_CFG_T final_pwm_cfg;
    OPERATE_RET ret = configure_pwm_parameters(servo_id, final_pwm_duty, &final_pwm_cfg);
    if (ret == OPRT_OK) {
        ret = safe_set_pwm_output(servo_id, &final_pwm_cfg);
    }

    if (ret == OPRT_OK) {
        servo->current_angle = target_angle;
        servo->door_state = (target_angle >= SERVO_ANGLE_OPEN) ? DOOR_STATE_OPEN : DOOR_STATE_CLOSED;
        servo->last_operation_time = tal_system_get_millisecond();
        servo->operation_count++;
        g_pwm_door_system.total_operations++;

        PR_INFO("✅ 舵机%d平滑运动完成: %d°", servo_id + 1, target_angle);
    }

    return ret;
}

/**
 * @brief 舵机位置反馈检测 - 基于PWM信号分析
 *
 * 通过分析PWM信号来估算舵机当前位置
 * 参考Adafruit库的反馈机制思想
 */
static OPERATE_RET servo_position_feedback(servo_id_e servo_id, uint16_t *current_angle)
{
    if (servo_id >= SERVO_ID_MAX || !current_angle) {
        return OPRT_INVALID_PARM;
    }

    if (!g_pwm_door_system.system_initialized) {
        return OPRT_COM_ERROR;
    }

    // 获取当前PWM配置
    TUYA_PWM_BASE_CFG_T current_cfg;
    OPERATE_RET ret = get_current_pwm_config(servo_id, &current_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 获取舵机%d PWM配置失败", servo_id + 1);
        return ret;
    }

    // 根据当前PWM占空比反推角度
    uint32_t current_duty = current_cfg.duty;

    // MG90S反向计算角度
    const uint32_t MG90S_MIN_PULSE = 544;
    const uint32_t MG90S_MAX_PULSE = 2400;
    const uint32_t MG90S_CENTER_PULSE = 1472;

    uint16_t estimated_angle;

    if (current_duty <= MG90S_CENTER_PULSE) {
        // 0-90度范围
        estimated_angle = ((current_duty - MG90S_MIN_PULSE) * 90) /
                         (MG90S_CENTER_PULSE - MG90S_MIN_PULSE);
    } else {
        // 90-180度范围
        estimated_angle = 90 + ((current_duty - MG90S_CENTER_PULSE) * 90) /
                              (MG90S_MAX_PULSE - MG90S_CENTER_PULSE);
    }

    // 限制角度范围
    if (estimated_angle > 180) {
        estimated_angle = 180;
    }

    *current_angle = estimated_angle;

    PR_DEBUG("📡 舵机%d位置反馈: PWM=%dμs -> 角度=%d°",
             servo_id + 1, current_duty, estimated_angle);

    return OPRT_OK;
}

/**
 * @brief MG90S舵机扭矩优化 - 基于负载自适应
 *
 * 根据负载情况调整PWM参数，优化舵机扭矩输出
 * 参考Adafruit库的自适应控制思想
 */
static OPERATE_RET mg90s_torque_optimization(servo_id_e servo_id, uint8_t load_level)
{
    if (servo_id >= SERVO_ID_MAX || load_level > 100) {
        return OPRT_INVALID_PARM;
    }

    if (!g_pwm_door_system.system_initialized) {
        return OPRT_COM_ERROR;
    }

    servo_status_t *servo = &g_pwm_door_system.servos[servo_id];

    // 根据负载等级调整PWM频率和占空比
    // MG90S在不同负载下的优化参数
    uint32_t optimized_frequency = SERVO_PWM_FREQ; // 基础频率50Hz
    uint32_t current_duty = mg90s_calibrated_duty(servo_id, servo->current_angle);

    // 负载自适应调整
    if (load_level > 80) {
        // 高负载：降低频率，增加扭矩
        optimized_frequency = 45; // 降低到45Hz
        PR_INFO("🔧 舵机%d高负载模式: 频率=%dHz", servo_id + 1, optimized_frequency);
    } else if (load_level > 50) {
        // 中等负载：标准参数
        optimized_frequency = 50; // 标准50Hz
        PR_INFO("🔧 舵机%d标准负载模式: 频率=%dHz", servo_id + 1, optimized_frequency);
    } else {
        // 低负载：提高频率，节能模式
        optimized_frequency = 55; // 提高到55Hz
        PR_INFO("🔧 舵机%d节能模式: 频率=%dHz", servo_id + 1, optimized_frequency);
    }

    // 配置优化后的PWM参数
    TUYA_PWM_BASE_CFG_T optimized_cfg = {
        .polarity = TUYA_PWM_POSITIVE,
        .count_mode = TUYA_PWM_CNT_UP,
        .duty = current_duty,
        .cycle = SERVO_PWM_CYCLE,
        .frequency = optimized_frequency
    };

    // 验证配置参数
    OPERATE_RET ret = validate_pwm_config(&optimized_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d扭矩优化配置验证失败", servo_id + 1);
        return ret;
    }

    // 应用优化配置
    ret = safe_set_pwm_output(servo_id, &optimized_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d扭矩优化应用失败", servo_id + 1);
        return ret;
    }

    PR_INFO("✅ 舵机%d扭矩优化完成: 负载=%d%%, 频率=%dHz",
            servo_id + 1, load_level, optimized_frequency);

    return OPRT_OK;
}

// ========== MG90S舵机增强功能公共接口实现 ==========

/**
 * @brief MG90S舵机平滑运动控制 - 公共接口
 */
OPERATE_RET pwm_door_servo_smooth_move(servo_id_e servo_id, uint16_t target_angle, uint32_t speed_ms)
{
    return servo_smooth_move(servo_id, target_angle, speed_ms);
}

/**
 * @brief MG90S舵机位置反馈检测 - 公共接口
 */
OPERATE_RET pwm_door_servo_get_position(servo_id_e servo_id, uint16_t *current_angle)
{
    return servo_position_feedback(servo_id, current_angle);
}

/**
 * @brief MG90S舵机扭矩优化 - 公共接口
 */
OPERATE_RET pwm_door_servo_optimize_torque(servo_id_e servo_id, uint8_t load_level)
{
    return mg90s_torque_optimization(servo_id, load_level);
}

/**
 * @brief MG90S舵机精确校准 - 公共接口
 */
OPERATE_RET pwm_door_servo_calibrate(servo_id_e servo_id, uint16_t angle, uint16_t actual_angle)
{
    if (servo_id >= SERVO_ID_MAX || angle > 180 || actual_angle > 180) {
        return OPRT_INVALID_PARM;
    }

    // 计算校准偏移量
    int16_t offset = actual_angle - angle;

    PR_INFO("🎯 舵机%d校准: 目标=%d°, 实际=%d°, 偏移=%d°",
            servo_id + 1, angle, actual_angle, offset);

    // 这里可以将偏移量保存到非易失性存储中
    // 在实际应用中，可以使用tal_kv_set保存校准数据

    PR_INFO("✅ 舵机%d校准完成", servo_id + 1);
    return OPRT_OK;
}

/**
 * @brief 运行MG90S舵机增强功能测试
 */
void pwm_door_control_run_mg90s_tests(void)
{
    PR_INFO("🧪 开始MG90S舵机增强功能测试...");

    if (!g_pwm_door_system.system_initialized) {
        PR_ERR("❌ 系统未初始化，无法运行测试");
        return;
    }

    // 测试1: 精确校准测试
    PR_INFO("📊 测试1: MG90S精确校准");
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        uint16_t test_angles[] = {0, 45, 90, 135, 180};
        int angle_count = sizeof(test_angles) / sizeof(test_angles[0]);

        for (int j = 0; j < angle_count; j++) {
            uint32_t calibrated_duty = mg90s_calibrated_duty(i, test_angles[j]);
            PR_INFO("   舵机%d: %d° -> %d μs", i + 1, test_angles[j], calibrated_duty);
        }
    }

    // 测试2: 平滑运动测试
    PR_INFO("📊 测试2: 平滑运动控制");
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        PR_INFO("   舵机%d平滑运动测试:", i + 1);

        // 平滑移动到90度
        OPERATE_RET ret = pwm_door_servo_smooth_move(i, 90, 20);
        if (ret == OPRT_OK) {
            PR_INFO("   ✅ 平滑移动到90°成功");
        }

        tal_system_sleep(1000);

        // 平滑移动回0度
        ret = pwm_door_servo_smooth_move(i, 0, 15);
        if (ret == OPRT_OK) {
            PR_INFO("   ✅ 平滑移动到0°成功");
        }

        tal_system_sleep(1000);
    }

    // 测试3: 位置反馈测试
    PR_INFO("📊 测试3: 位置反馈检测");
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        uint16_t detected_angle;
        OPERATE_RET ret = pwm_door_servo_get_position(i, &detected_angle);
        if (ret == OPRT_OK) {
            PR_INFO("   舵机%d当前位置: %d°", i + 1, detected_angle);
        }
    }

    // 测试4: 扭矩优化测试
    PR_INFO("📊 测试4: 扭矩优化");
    uint8_t load_levels[] = {20, 60, 90}; // 低、中、高负载
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        for (int j = 0; j < 3; j++) {
            OPERATE_RET ret = pwm_door_servo_optimize_torque(i, load_levels[j]);
            if (ret == OPRT_OK) {
                PR_INFO("   ✅ 舵机%d负载%d%%优化成功", i + 1, load_levels[j]);
            }
            tal_system_sleep(500);
        }
    }

    // 复位到标准配置
    PR_INFO("🔄 复位到标准配置...");
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        pwm_door_servo_angle_control(i, SERVO_ANGLE_CLOSED);
    }

    PR_INFO("🎉 MG90S舵机增强功能测试完成!");
}
