#!/bin/bash

# 编译测试脚本
# 用于验证代码是否能够成功编译

echo "🧪 开始编译测试..."

# 设置编译器和基本参数
CC="gcc"
CFLAGS="-Wall -Wextra -Werror -std=c99"
INCLUDES="-I./include -I../../src/tal_system/include -I../../src/common/include"

# 定义源文件
SOURCES="
src/pwm_door_control.c
src/pca9685_driver.c
src/pca9685_servo_test.c
compile_test.c
"

# 创建临时目录
mkdir -p temp_build

echo "📁 创建模拟头文件..."

# 创建模拟的系统头文件
cat > temp_build/tal_api.h << 'EOF'
#ifndef TAL_API_H
#define TAL_API_H

#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

// 布尔值定义
#ifndef TRUE
#define TRUE 1
#define FALSE 0
#endif

typedef int OPERATE_RET;
#define OPRT_OK 0
#define OPRT_INVALID_PARM -1
#define OPRT_COM_ERROR -2

typedef enum {
    TUYA_GPIO_NUM_6 = 6,
    TUYA_GPIO_NUM_7 = 7
} TUYA_GPIO_NUM_E;

typedef enum {
    TUYA_PWM_NUM_0 = 0,
    TUYA_PWM_NUM_1 = 1
} TUYA_PWM_NUM_E;

typedef enum {
    TUYA_PWM_POSITIVE = 0,
    TUYA_PWM_NEGATIVE = 1
} TUYA_PWM_POLARITY_E;

typedef enum {
    TUYA_PWM_CNT_UP = 0,
    TUYA_PWM_CNT_UP_AND_DOWN = 1
} TUYA_PWM_CNT_MODE_E;

typedef struct {
    uint32_t frequency;
    uint32_t duty;
    uint32_t cycle;
    TUYA_PWM_POLARITY_E polarity;
    TUYA_PWM_CNT_MODE_E count_mode;
} TUYA_PWM_BASE_CFG_T;

// 线程相关定义
typedef void* THREAD_HANDLE;
typedef struct {
    uint32_t priority;
    uint32_t stackDepth;
    const char* thrdname;
} THREAD_CFG_T;

// 线程优先级定义
#define THREAD_PRIO_2 2

// 模拟函数声明
uint32_t tal_system_get_millisecond(void);
void tal_system_sleep(uint32_t ms);
OPERATE_RET tkl_pwm_init(TUYA_PWM_NUM_E pwm_num, const TUYA_PWM_BASE_CFG_T *cfg);
OPERATE_RET tkl_pwm_start(TUYA_PWM_NUM_E pwm_num);
OPERATE_RET tkl_pwm_stop(TUYA_PWM_NUM_E pwm_num);
OPERATE_RET tkl_pwm_deinit(TUYA_PWM_NUM_E pwm_num);
OPERATE_RET tkl_pwm_info_get(TUYA_PWM_NUM_E pwm_num, TUYA_PWM_BASE_CFG_T *cfg);
OPERATE_RET tkl_pwm_info_set(TUYA_PWM_NUM_E pwm_num, const TUYA_PWM_BASE_CFG_T *cfg);

// 线程函数声明
OPERATE_RET tal_thread_create(THREAD_HANDLE *handle, const char *name, uint32_t stack_size, uint32_t priority, void (*func)(void*), void *param);
OPERATE_RET tal_thread_delete(THREAD_HANDLE handle);
OPERATE_RET tal_thread_create_and_start(THREAD_HANDLE *handle, void *param1, void *param2, void (*func)(void*), void *param, const THREAD_CFG_T *cfg);

// 日志宏 - 简化版本
#ifndef PR_INFO
#define PR_INFO(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define PR_ERR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)
#define PR_WARN(fmt, ...) printf("[WARN] " fmt "\n", ##__VA_ARGS__)
#define PR_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#endif

#endif
EOF

cat > temp_build/tuya_cloud_types.h << 'EOF'
#ifndef TUYA_CLOUD_TYPES_H
#define TUYA_CLOUD_TYPES_H

#include <stdint.h>
#include <stdbool.h>

typedef int OPERATE_RET;
#define OPRT_OK 0
#define OPRT_INVALID_PARM -1
#define OPRT_COM_ERROR -2

#endif
EOF

cat > temp_build/tkl_pwm.h << 'EOF'
#ifndef TKL_PWM_H
#define TKL_PWM_H

#include "tal_api.h"

#endif
EOF

cat > temp_build/tkl_output.h << 'EOF'
#ifndef TKL_OUTPUT_H
#define TKL_OUTPUT_H

#include "tal_api.h"

#endif
EOF

cat > temp_build/tkl_i2c.h << 'EOF'
#ifndef TKL_I2C_H
#define TKL_I2C_H

#include "tal_api.h"

typedef enum {
    TUYA_I2C_NUM_0 = 0,
    TUYA_I2C_NUM_1 = 1
} TUYA_I2C_NUM_E;

// I2C常量定义
#define TUYA_IIC_ADDRESS_7BIT 0
#define TUYA_IIC_BUS_SPEED_100K 100000
#define PCA9685_I2C_PORT TUYA_I2C_NUM_0

typedef struct {
    uint32_t role;
    uint32_t addr_width;
    uint32_t speed;
    uint32_t timeout;
} TUYA_I2C_BASE_CFG_T;

// 兼容性定义
typedef TUYA_I2C_BASE_CFG_T TUYA_IIC_BASE_CFG_T;

// I2C模式定义
#define TUYA_IIC_MODE_MASTER 0

OPERATE_RET tkl_i2c_init(TUYA_I2C_NUM_E i2c_num, const TUYA_I2C_BASE_CFG_T *cfg);
OPERATE_RET tkl_i2c_deinit(TUYA_I2C_NUM_E i2c_num);
OPERATE_RET tkl_i2c_master_send(TUYA_I2C_NUM_E i2c_num, uint16_t dev_addr, const uint8_t *data, uint32_t size, bool xfer_pending);
OPERATE_RET tkl_i2c_master_receive(TUYA_I2C_NUM_E i2c_num, uint16_t dev_addr, uint8_t *data, uint32_t size, bool xfer_pending);

#endif
EOF

cat > temp_build/tkl_pinmux.h << 'EOF'
#ifndef TKL_PINMUX_H
#define TKL_PINMUX_H

#include "tal_api.h"

typedef enum {
    TUYA_PIN_FUNC_GPIO = 0,
    TUYA_PIN_FUNC_I2C = 1,
    TUYA_IIC0_SCL = 2,
    TUYA_IIC0_SDA = 3
} TUYA_PIN_FUNC_E;

OPERATE_RET tkl_pinmux_config(TUYA_GPIO_NUM_E pin, TUYA_PIN_FUNC_E func);
OPERATE_RET tkl_io_pinmux_config(TUYA_GPIO_NUM_E pin, TUYA_PIN_FUNC_E func);

#endif
EOF

cat > temp_build/tal_log.h << 'EOF'
#ifndef TAL_LOG_H
#define TAL_LOG_H

#include <stdio.h>

// 日志级别定义
typedef enum {
    TAL_LOG_LEVEL_ERR = 0,
    TAL_LOG_LEVEL_WARN = 1,
    TAL_LOG_LEVEL_INFO = 2,
    TAL_LOG_LEVEL_DEBUG = 3
} TAL_LOG_LEVEL_E;

// 日志函数声明
void tal_log_print(TAL_LOG_LEVEL_E level, const char* file, int line, const char* fmt, ...);

// 日志宏定义 - 使用简化版本避免重复定义
#define _THIS_FILE_NAME_ __FILE__

#endif
EOF

cat > temp_build/tal_system.h << 'EOF'
#ifndef TAL_SYSTEM_H
#define TAL_SYSTEM_H

#include "tal_api.h"

#endif
EOF

echo "🔧 开始编译测试..."

# 编译测试（只检查语法，不链接）
for source in $SOURCES; do
    echo "  检查 $source..."
    $CC $CFLAGS $INCLUDES -I./temp_build -c $source -o /dev/null
    if [ $? -ne 0 ]; then
        echo "❌ $source 编译失败"
        exit 1
    fi
done

if [ $? -eq 0 ]; then
    echo "✅ 编译测试成功！所有语法错误已修复。"
    echo "📊 测试结果："
    echo "   - 头文件包含: ✅"
    echo "   - 函数声明: ✅"
    echo "   - 结构体定义: ✅"
    echo "   - 常量定义: ✅"
    echo "   - 语法检查: ✅"
else
    echo "❌ 编译测试失败！仍有语法错误需要修复。"
    exit 1
fi

# 清理临时文件
rm -rf temp_build

echo "🎉 编译测试完成！"
