/**
 * @file test_display_compile.c
 * @brief 显示模块编译测试文件
 * 
 * 用于测试显示模块的编译是否成功
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "app_display.h"
#include <string.h>

/**
 * @brief 测试显示枚举类型
 */
void test_display_enums(void)
{
    // 测试所有显示类型枚举
    TY_DISPLAY_TYPE_E types[] = {
        TY_DISPLAY_TP_USER_MSG,
        TY_DISPLAY_TP_ASSISTANT_MSG,
        TY_DISPLAY_TP_ASSISTANT_MSG_STREAM_START,
        TY_DISPLAY_TP_ASSISTANT_MSG_STREAM_DATA,
        TY_DISPLAY_TP_ASSISTANT_MSG_STREAM_END,
        TY_DISPLAY_TP_SYSTEM_MSG,
        TY_DISPLAY_TP_EMOTION,
        TY_DISPLAY_TP_STATUS,
        TY_DISPLAY_TP_NOTIFICATION,
        TY_DISPLAY_TP_NETWORK,
        TY_DISPLAY_TP_CHAT_MODE,
        TY_DISPLAY_TP_DOOR_NUMBER,
        TY_DISPLAY_TP_MAX
    };
    
    // 避免未使用变量警告
    (void)types;
}

/**
 * @brief 测试显示函数调用
 */
void test_display_functions(void)
{
    // 测试显示初始化
    app_display_init();
    
    // 测试发送消息
    const char *test_msg = "Test Message";
    app_display_send_msg(TY_DISPLAY_TP_USER_MSG, (uint8_t*)test_msg, strlen(test_msg));
    app_display_send_msg(TY_DISPLAY_TP_ASSISTANT_MSG, (uint8_t*)test_msg, strlen(test_msg));
    app_display_send_msg(TY_DISPLAY_TP_SYSTEM_MSG, (uint8_t*)test_msg, strlen(test_msg));
    app_display_send_msg(TY_DISPLAY_TP_EMOTION, (uint8_t*)EMOJI_HAPPY, strlen(EMOJI_HAPPY));
    app_display_send_msg(TY_DISPLAY_TP_STATUS, (uint8_t*)test_msg, strlen(test_msg));
    app_display_send_msg(TY_DISPLAY_TP_NOTIFICATION, (uint8_t*)test_msg, strlen(test_msg));
    app_display_send_msg(TY_DISPLAY_TP_NETWORK, (uint8_t*)test_msg, strlen(test_msg));
    app_display_send_msg(TY_DISPLAY_TP_CHAT_MODE, (uint8_t*)test_msg, strlen(test_msg));
}

/**
 * @brief 测试WiFi状态枚举
 */
void test_wifi_status(void)
{
    UI_WIFI_STATUS_E status = UI_WIFI_STATUS_DISCONNECTED;
    status = UI_WIFI_STATUS_GOOD;
    status = UI_WIFI_STATUS_FAIR;
    status = UI_WIFI_STATUS_WEAK;
    
    // 避免未使用变量警告
    (void)status;
}

/**
 * @brief 测试表情符号常量
 */
void test_emoji_constants(void)
{
    const char *emojis[] = {
        EMOJI_NEUTRAL,
        EMOJI_SAD,
        EMOJI_ANGRY,
        EMOJI_SURPRISE,
        EMOJI_CONFUSED,
        EMOJI_THINKING,
        EMOJI_HAPPY,
        EMOJI_TOUCH,
        EMOJI_FEARFUL,
        EMOJI_DISAPPOINTED,
        EMOJI_ANNOYED
    };
    
    // 避免未使用变量警告
    (void)emojis;
}

/**
 * @brief 主测试函数
 */
void display_compile_test_main(void)
{
    test_display_enums();
    test_display_functions();
    test_wifi_status();
    test_emoji_constants();
}
