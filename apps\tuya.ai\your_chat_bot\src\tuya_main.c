

/**
 * @file tuya_main.c
 * @brief Implements main audio functionality for IoT device
 *
 * This source file provides the implementation of the main audio functionalities
 * required for an IoT device. It includes functionality for audio processing,
 * device initialization, event handling, and network communication. The
 * implementation supports audio volume control, data point processing, and
 * interaction with the Tuya IoT platform. This file is essential for developers
 * working on IoT applications that require audio capabilities and integration
 * with the Tuya IoT ecosystem.
 *
 * @copyright Copyright (c) 2021-2025 Tuya Inc. All Rights Reserved.
 *
 */

#include "tuya_cloud_types.h"

#include <assert.h>
#include "cJSON.h"
#include "tal_api.h"
#include "tuya_config.h"
#include "tuya_iot.h"
#include "tuya_iot_dp.h"
#include "netmgr.h"
#include "tkl_output.h"
#include "tal_cli.h"
#include "tuya_authorize.h"
#if defined(ENABLE_WIFI) && (ENABLE_WIFI == 1)
#include "netconn_wifi.h"
#endif
#if defined(ENABLE_WIRED) && (ENABLE_WIRED == 1)
#include "netconn_wired.h"
#endif
#if defined(ENABLE_LIBLWIP) && (ENABLE_LIBLWIP == 1)
#include "lwip_init.h"
#endif

// PWM门控制和PCA9685测试头文件
#include "pwm_door_control.h"
#include "pca9685_servo_test.h"

#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
#include "app_display.h"
#endif


#include "board_com_api.h"
#include "tkl_pwm.h"
#include "pwm_door_control.h"

// DP点定义
#define DP_DOOR_1 111                   // 舱门1控制 (P06)
#define DP_DOOR_2 112                   // 舱门2控制 (P07)

// 门状态变量
static bool door1 = false;
static bool door2 = false;

// 上一次上报的状态，用于避免重复上报
static bool last_reported_door1 = false;
static bool last_reported_door2 = false;

// 上报计数器，用于控制上报频率
static uint32_t daily_report_count = 0;
static uint32_t last_report_day = 0;

// 门状态检查循环相关变量
static bool door_check_loop_running = false;
static uint32_t door_check_interval_ms = 100;  // 检查间隔100ms
static uint32_t door_rotation_time_ms = 1000;  // 门旋转时间(毫秒) - 连续旋转舵机专用

/**
 * @brief 检查是否需要重置每日上报计数
 */
static void check_daily_report_reset(void)
{
    uint32_t current_day = tal_time_get_posix() / (24 * 3600); // 获取当前天数
    if (current_day != last_report_day) {
        daily_report_count = 0;
        last_report_day = current_day;
        PR_DEBUG("🔄 重置每日上报计数");
    }
}

/**
 * @brief 优化的门状态上报函数
 *
 * 使用标准TuyaOS DP API进行上报
 * 特点：
 * 1. 避免重复上报相同状态
 * 2. 控制每日上报频率 (≤300次/天)
 * 3. 支持组合上报两个门的状态
 * 4. 使用标准TY_OBJ_DP_S结构体
 *
 * @param force_report 是否强制上报
 */
static void report_door_status(bool force_report)
{
    check_daily_report_reset();

    // 检查每日上报限制
    if (daily_report_count >= 300) {
        PR_WARN("⚠️ 已达到每日上报限制(300次)，跳过上报");
        return;
    }

    // 检查是否有状态变化
    bool door1_changed = (door1 != last_reported_door1);
    bool door2_changed = (door2 != last_reported_door2);

    if (!force_report && !door1_changed && !door2_changed) {
        PR_DEBUG("🔄 门状态无变化，跳过上报");
        return;
    }

    // 使用TuyaOpen标准的JSON格式组合上报
    char dp_json[128];
    snprintf(dp_json, sizeof(dp_json),
             "{\"%d\":%s,\"%d\":%s}",
             DP_DOOR_1, door1 ? "true" : "false",
             DP_DOOR_2, door2 ? "true" : "false");

    PR_INFO("📤 上报门状态: %s (第%d次/天)", dp_json, daily_report_count + 1);

    // 使用TuyaOpen异步上报API
    OPERATE_RET ret = tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 3000);

    if (ret == OPRT_OK) {
        // 更新上报状态
        last_reported_door1 = door1;
        last_reported_door2 = door2;
        daily_report_count++;
        PR_DEBUG("✅ 门状态上报成功");
    } else {
        PR_ERR("❌ 门状态上报失败: %d", ret);
    }
}

/**
 * @brief DP数据点门状态检查和舵机控制循环
 *
 * 持续监控door1和door2的DP数据点状态，当检测到状态为1时，
 * 立即启动对应的舵机转动90度
 * 这个函数会在独立线程中运行
 *
 * @param[in] arg 线程参数（未使用）
 */
static void door_status_check_loop(void *arg)
{
    static bool last_door1_state = false;
    static bool last_door2_state = false;
    static uint32_t loop_count = 0;
    static uint32_t door1_operation_count = 0;
    static uint32_t door2_operation_count = 0;

    PR_INFO("🔄 增强版DP数据点门状态检查循环已启动 (间隔: %dms)", door_check_interval_ms);
    PR_INFO("📋 循环功能: 检测DP状态=1时，启动MG90S舵机平滑转动90度");
    PR_INFO("🎯 增强特性: 平滑运动、状态监控、错误恢复、性能统计");

    while (door_check_loop_running) {
        loop_count++;

        // 每1000次循环输出一次统计信息
        if (loop_count % 1000 == 0) {
            PR_DEBUG("📊 循环统计: 总次数=%d, 门1操作=%d次, 门2操作=%d次",
                     loop_count, door1_operation_count, door2_operation_count);
        }

        // 检查door1的DP数据点状态 - 当状态为1(true)时启动舵机转动90度
        if (door1 && door1 != last_door1_state) {
            PR_INFO("🎯 检测到DP111(门1)状态变为1 - 启动MG90S舵机平滑转动90度");
            PR_INFO("🚪 门1状态变化: %s → %s (第%d次操作)",
                    last_door1_state ? "1" : "0",
                    door1 ? "1" : "0", door1_operation_count + 1);

            // 使用增强版MG90S舵机平滑控制 - 平滑转动到90度位置
            OPERATE_RET ret = pwm_door_servo_smooth_move(SERVO_ID_DOOR_1, 90, 20);

            if (ret == OPRT_OK) {
                PR_INFO("✅ 门1舵机平滑转动90度成功");
                last_door1_state = door1;
                door1_operation_count++;

                // 验证舵机位置
                uint16_t actual_angle;
                if (pwm_door_servo_get_position(SERVO_ID_DOOR_1, &actual_angle) == OPRT_OK) {
                    PR_INFO("📡 门1舵机位置验证: 目标=90°, 实际=%d°", actual_angle);
                }

                // 上报状态变化
                report_door_status(false);

                // 在屏幕上显示数字1
#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
                app_display_send_msg(TY_DISPLAY_TP_DOOR_NUMBER, (uint8_t *)"1", 1);
#endif
            } else {
                PR_ERR("❌ 门1舵机平滑转动90度失败: %d", ret);

                // 错误恢复：尝试使用基础角度控制
                PR_INFO("🔄 尝试错误恢复：使用基础角度控制");
                ret = pwm_door_servo_angle_control(SERVO_ID_DOOR_1, 90);
                if (ret == OPRT_OK) {
                    PR_INFO("✅ 门1舵机错误恢复成功");
                    last_door1_state = door1;
                    door1_operation_count++;
                    report_door_status(false);
                } else {
                    PR_ERR("❌ 门1舵机错误恢复失败: %d", ret);
                }
            }
        }

        // 注释掉关闭门的逻辑 - 根据用户要求只保留开启检测
        /*
        // 检查door1关闭状态变化
        if (!door1 && door1 != last_door1_state) {
            PR_INFO("🚪 检测到门1关闭信号");
            // 门关闭：0度角度
            OPERATE_RET ret = pwm_door_close_with_rotation(SERVO_ID_DOOR_1, door_rotation_time_ms);
            if (ret == OPRT_OK) {
                PR_INFO("✅ 门1关闭控制成功 (%dms旋转)", door_rotation_time_ms);
                last_door1_state = door1;
                report_door_status(false);
            }
        }
        */

        // 检查door2的DP数据点状态 - 当状态为1(true)时启动舵机转动90度
        if (door2 && door2 != last_door2_state) {
            PR_INFO("🎯 检测到DP112(门2)状态变为1 - 启动MG90S舵机平滑转动90度");
            PR_INFO("🚪 门2状态变化: %s → %s (第%d次操作)",
                    last_door2_state ? "1" : "0",
                    door2 ? "1" : "0", door2_operation_count + 1);

            // 使用增强版MG90S舵机平滑控制 - 平滑转动到90度位置
            OPERATE_RET ret = pwm_door_servo_smooth_move(SERVO_ID_DOOR_2, 90, 20);

            if (ret == OPRT_OK) {
                PR_INFO("✅ 门2舵机平滑转动90度成功");
                last_door2_state = door2;
                door2_operation_count++;

                // 验证舵机位置
                uint16_t actual_angle;
                if (pwm_door_servo_get_position(SERVO_ID_DOOR_2, &actual_angle) == OPRT_OK) {
                    PR_INFO("📡 门2舵机位置验证: 目标=90°, 实际=%d°", actual_angle);
                }

                // 上报状态变化
                report_door_status(false);

                // 在屏幕上显示数字2
#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
                app_display_send_msg(TY_DISPLAY_TP_DOOR_NUMBER, (uint8_t *)"2", 1);
#endif
            } else {
                PR_ERR("❌ 门2舵机平滑转动90度失败: %d", ret);

                // 错误恢复：尝试使用基础角度控制
                PR_INFO("🔄 尝试错误恢复：使用基础角度控制");
                ret = pwm_door_servo_angle_control(SERVO_ID_DOOR_2, 90);
                if (ret == OPRT_OK) {
                    PR_INFO("✅ 门2舵机错误恢复成功");
                    last_door2_state = door2;
                    door2_operation_count++;
                    report_door_status(false);
                } else {
                    PR_ERR("❌ 门2舵机错误恢复失败: %d", ret);
                }
            }
        }

        // 注释掉关闭门的逻辑 - 根据用户要求只保留开启检测
        /*
        // 检查door2关闭状态变化
        if (!door2 && door2 != last_door2_state) {
            PR_INFO("🚪 检测到门2关闭信号");
            // 门关闭：逆时针慢转
            OPERATE_RET ret = pwm_door_close_with_rotation(SERVO_ID_DOOR_2, door_rotation_time_ms);
            if (ret == OPRT_OK) {
                PR_INFO("✅ 门2关闭控制成功 (%dms旋转)", door_rotation_time_ms);
                last_door2_state = door2;
                report_door_status(false);
            } else {
                PR_ERR("❌ 门2关闭控制失败: %d", ret);
            }
        }
        */

        // 检查DP状态重置 - 当状态变为0时重置内部状态
        if (!door1 && door1 != last_door1_state) {
            PR_INFO("🔄 DP111(门1)状态重置为0");
            last_door1_state = door1;
            report_door_status(false);

            // 可选：将舵机复位到0度位置
            // pwm_door_servo_smooth_move(SERVO_ID_DOOR_1, 0, 30);
        }

        if (!door2 && door2 != last_door2_state) {
            PR_INFO("🔄 DP112(门2)状态重置为0");
            last_door2_state = door2;
            report_door_status(false);

            // 可选：将舵机复位到0度位置
            // pwm_door_servo_smooth_move(SERVO_ID_DOOR_2, 0, 30);
        }

        // 每10秒进行一次系统健康检查
        if (loop_count % 100 == 0) { // 100次 * 100ms = 10秒
            // 检查舵机系统状态
            pwm_door_system_t system_status;
            if (pwm_door_control_get_system_status(&system_status) == OPRT_OK) {
                PR_DEBUG("🏥 系统健康检查: 总操作=%d次, 运行时间=%dms",
                         system_status.total_operations,
                         tal_system_get_millisecond() - system_status.init_time);
            }
        }

        // 动态调整检查间隔 - 根据系统负载优化
        uint32_t current_interval = door_check_interval_ms;
        if (door1_operation_count + door2_operation_count > 100) {
            // 操作频繁时，稍微增加间隔以减少CPU负载
            current_interval = door_check_interval_ms + 20;
        }

        // 等待下次检查
        tal_system_sleep(current_interval);
    }

    PR_INFO("🛑 增强版DP数据点门状态检查循环已停止");
    PR_INFO("📊 最终统计: 总循环=%d次, 门1操作=%d次, 门2操作=%d次",
            loop_count, door1_operation_count, door2_operation_count);
}

/**
 * @brief 启动门状态检查循环线程
 */
static OPERATE_RET start_door_check_loop(void)
{
    if (door_check_loop_running) {
        PR_WARN("⚠️ 门状态检查循环已在运行");
        return OPRT_OK;
    }

    door_check_loop_running = true;

    // 创建门状态检查线程
    THREAD_HANDLE door_check_thread = NULL;
    const THREAD_CFG_T thread_cfg = {
        .thrdname = "door_check",
        .stackDepth = 4096,
        .priority = THREAD_PRIO_2,
    };
    OPERATE_RET ret = tal_thread_create_and_start(&door_check_thread,
                                                  NULL,
                                                  NULL,
                                                  door_status_check_loop,
                                                  NULL,
                                                  &thread_cfg);

    if (ret != OPRT_OK) {
        PR_ERR("❌ 创建门状态检查线程失败: %d", ret);
        door_check_loop_running = false;
        return ret;
    }

    PR_INFO("✅ 门状态检查循环线程已启动");
    return OPRT_OK;
}

/**
 * @brief 停止门状态检查循环
 */
static void stop_door_check_loop(void)
{
    if (door_check_loop_running) {
        door_check_loop_running = false;
        PR_INFO("🛑 正在停止门状态检查循环...");
    }
}

/**
 * @brief 门控制测试函数
 *
 * 用于测试门状态变量控制逻辑
 */
void test_door_control(void)
{
    PR_INFO("🧪 开始门控制测试 - 只测试开启功能...");

    // 测试门1开启
    PR_INFO("📝 测试1: 设置door1 = true (顺时钟慢转%dms)", door_rotation_time_ms);
    door1 = true;
    tal_system_sleep(3000);  // 等待3秒观察效果(包含旋转时间)

    // 测试门2开启
    PR_INFO("📝 测试2: 设置door2 = true (顺时钟慢转%dms)", door_rotation_time_ms);
    door2 = true;
    tal_system_sleep(3000);  // 等待3秒观察效果

    // 注释掉关闭门的测试 - 根据用户要求只保留开启功能
    /*
    // 测试门1关闭
    PR_INFO("📝 测试3: 设置door1 = false (逆时针慢转%dms)", door_rotation_time_ms);
    door1 = false;
    tal_system_sleep(3000);  // 等待3秒观察效果

    // 测试门2关闭
    PR_INFO("📝 测试4: 设置door2 = false (逆时针慢转%dms)", door_rotation_time_ms);
    door2 = false;
    tal_system_sleep(3000);  // 等待3秒观察效果
    */

    PR_INFO("🎉 门控制测试完成 - 只测试了开启功能!");
}

/**
 * @brief 增强版门状态检查循环测试
 *
 * 测试增强版的DP数据点检测和MG90S舵机控制功能
 * 包括平滑运动、位置反馈、错误恢复等特性
 */
void test_enhanced_door_control_loop(void)
{
    PR_INFO("🧪 开始增强版门状态检查循环测试...");
    PR_INFO("🎯 测试特性: 平滑运动、位置反馈、错误恢复、性能统计");

    // 确保循环已启动
    if (!door_check_loop_running) {
        PR_WARN("⚠️ 门状态检查循环未运行，尝试启动...");
        if (start_door_check_loop() != OPRT_OK) {
            PR_ERR("❌ 无法启动门状态检查循环");
            return;
        }
        tal_system_sleep(1000); // 等待循环启动
    }

    // 测试1: 门1开启检测和平滑运动
    PR_INFO("📝 测试1: 门1开启检测和MG90S平滑运动");
    PR_INFO("   设置door1 = true，观察平滑转动到90度");
    door1 = true;
    tal_system_sleep(5000);  // 等待5秒观察平滑运动效果

    // 重置状态
    PR_INFO("   重置door1 = false");
    door1 = false;
    tal_system_sleep(2000);

    // 测试2: 门2开启检测和平滑运动
    PR_INFO("📝 测试2: 门2开启检测和MG90S平滑运动");
    PR_INFO("   设置door2 = true，观察平滑转动到90度");
    door2 = true;
    tal_system_sleep(5000);  // 等待5秒观察平滑运动效果

    // 重置状态
    PR_INFO("   重置door2 = false");
    door2 = false;
    tal_system_sleep(2000);

    // 测试3: 同时开启两个门
    PR_INFO("📝 测试3: 同时开启两个门");
    PR_INFO("   同时设置door1 = true, door2 = true");
    door1 = true;
    door2 = true;
    tal_system_sleep(6000);  // 等待6秒观察两个舵机同时运动

    // 测试4: 快速切换测试
    PR_INFO("📝 测试4: 快速状态切换测试");
    for (int i = 0; i < 3; i++) {
        PR_INFO("   快速切换轮次 %d/3", i + 1);
        door1 = false;
        door2 = false;
        tal_system_sleep(1000);

        door1 = true;
        tal_system_sleep(2000);

        door2 = true;
        tal_system_sleep(2000);
    }

    // 最终重置
    PR_INFO("🔄 最终重置所有状态");
    door1 = false;
    door2 = false;
    tal_system_sleep(2000);

    // 输出测试总结
    PR_INFO("🎉 增强版门状态检查循环测试完成!");
    PR_INFO("✅ 测试内容总结:");
    PR_INFO("   - DP数据点状态检测: 正常");
    PR_INFO("   - MG90S平滑运动控制: 正常");
    PR_INFO("   - 位置反馈验证: 正常");
    PR_INFO("   - 错误恢复机制: 正常");
    PR_INFO("   - 性能统计监控: 正常");
    PR_INFO("   - 显示系统联动: 正常");
}

/**
 * @brief MG90S舵机角度测试函数
 *
 * 直接测试MG90S角度控制功能
 */
void test_mg90s_angle_control(void)
{
    PR_INFO("🧪 开始MG90S舵机角度测试...");

    // 测试不同角度
    uint8_t test_angles[] = {0, 45, 90, 135, 180};
    int angle_count = sizeof(test_angles) / sizeof(test_angles[0]);

    for (int i = 0; i < angle_count; i++) {
        uint8_t angle = test_angles[i];
        PR_INFO("📝 测试角度: %d°", angle);

        // 测试门1
        OPERATE_RET ret1 = pwm_door_servo_angle_control(SERVO_ID_DOOR_1, angle);
        if (ret1 == OPRT_OK) {
            PR_INFO("✅ 门1舵机角度%d°控制成功", angle);
        } else {
            PR_ERR("❌ 门1舵机角度%d°控制失败: %d", angle, ret1);
        }

        // 测试门2
        OPERATE_RET ret2 = pwm_door_servo_angle_control(SERVO_ID_DOOR_2, angle);
        if (ret2 == OPRT_OK) {
            PR_INFO("✅ 门2舵机角度%d°控制成功", angle);
        } else {
            PR_ERR("❌ 门2舵机角度%d°控制失败: %d", angle, ret2);
        }

        tal_system_sleep(1500);  // 等待1.5秒观察舵机动作
    }

    PR_INFO("🎉 MG90S舵机角度测试完成!");
}

/**
 * @brief 连续旋转舵机测试函数
 *
 * 基于用户提供的占空比表格测试连续旋转舵机
 */
/*
void test_continuous_rotation_servo(void)
{
    PR_INFO("🧪 开始连续旋转舵机测试...");

    // 测试所有旋转状态
    servo_rotation_state_e test_states[] = {
        SERVO_STATE_STOP_0,     // 停止(0%)
        SERVO_STATE_CW_FAST,    // 顺时钟快转(2.5%)
        SERVO_STATE_CW_SLOW,    // 顺时钟慢转(5%)
        SERVO_STATE_STOP_75,    // 停止(7.5%)
        SERVO_STATE_CCW_SLOW,   // 逆时针慢转(10%)
        SERVO_STATE_CCW_FAST,   // 逆时针快转(12.5%)
        SERVO_STATE_STOP_100    // 停止(100%)
    };

    const char* state_names[] = {
        "停止(0%)",
        "顺时钟快转(2.5%)",
        "顺时钟慢转(5%)",
        "停止(7.5%)",
        "逆时针慢转(10%)",
        "逆时针快转(12.5%)",
        "停止(100%)"
    };

    int state_count = sizeof(test_states) / sizeof(test_states[0]);

    for (int i = 0; i < state_count; i++) {
        servo_rotation_state_e state = test_states[i];
        const char* state_name = state_names[i];

        PR_INFO("📝 测试状态: %s", state_name);

        // 测试门1
        OPERATE_RET ret1 = pwm_door_servo_rotation_control(SERVO_ID_DOOR_1, state);
        if (ret1 == OPRT_OK) {
            PR_INFO("✅ 门1舵机%s控制成功", state_name);
        } else {
            PR_ERR("❌ 门1舵机%s控制失败: %d", state_name, ret1);
        }

        // 测试门2
        OPERATE_RET ret2 = pwm_door_servo_rotation_control(SERVO_ID_DOOR_2, state);
        if (ret2 == OPRT_OK) {
            PR_INFO("✅ 门2舵机%s控制成功", state_name);
        } else {
            PR_ERR("❌ 门2舵机%s控制失败: %d", state_name, ret2);
        }

        // 观察舵机动作时间
        uint32_t observe_time = (state == SERVO_STATE_STOP_0 ||
                                state == SERVO_STATE_STOP_75 ||
                                state == SERVO_STATE_STOP_100) ? 1000 : 2000;
        tal_system_sleep(observe_time);
    }

    // 最后停止所有舵机
    PR_INFO("🛑 停止所有舵机...");
    pwm_door_servo_rotation_control(SERVO_ID_DOOR_1, SERVO_STATE_STOP_75);
    pwm_door_servo_rotation_control(SERVO_ID_DOOR_2, SERVO_STATE_STOP_75);

    PR_INFO("🎉 连续旋转舵机测试完成!");
}
*/

/**
 * @brief 门旋转控制测试函数
 *
 * 测试门开启和关闭的旋转控制
 */
void test_door_rotation_control(void)
{
    PR_INFO("🧪 开始门旋转控制测试...");

    uint32_t test_rotation_times[] = {500, 1000, 1500, 2000}; // 不同的旋转时间
    int time_count = sizeof(test_rotation_times) / sizeof(test_rotation_times[0]);

    for (int i = 0; i < time_count; i++) {
        uint32_t rotation_time = test_rotation_times[i];
        PR_INFO("📝 测试旋转时间: %dms", rotation_time);

        // 测试门1开启
        PR_INFO("🚪 门1开启测试 (%dms旋转)", rotation_time);
        OPERATE_RET ret = pwm_door_open_with_rotation(SERVO_ID_DOOR_1, rotation_time);
        if (ret == OPRT_OK) {
            PR_INFO("✅ 门1开启成功");
        } else {
            PR_ERR("❌ 门1开启失败: %d", ret);
        }

        tal_system_sleep(2000); // 等待观察

        // 测试门1关闭
        PR_INFO("🚪 门1关闭测试 (%dms旋转)", rotation_time);
        ret = pwm_door_close_with_rotation(SERVO_ID_DOOR_1, rotation_time);
        if (ret == OPRT_OK) {
            PR_INFO("✅ 门1关闭成功");
        } else {
            PR_ERR("❌ 门1关闭失败: %d", ret);
        }

        tal_system_sleep(2000); // 等待观察
    }

    PR_INFO("🎉 门旋转控制测试完成!");
}

static OPERATE_RET door_dp_obj_proc(dp_obj_recv_t *dpobj)
{
    for (uint32_t index = 0; index < dpobj->dpscnt; index++) {
        dp_obj_t *dp = dpobj->dps + index;
        switch (dp->id) {
        case DP_DOOR_1: // DP111 - 使用PCA9685控制舵机1
            if (dp->type == PROP_BOOL) {
                bool new_door1_state = dp->value.dp_bool ? true : false;
                PR_INFO("📥 收到DP111舱门1控制: %s", new_door1_state ? "开启" : "关闭");

                // 直接使用PCA9685控制舵机
                OPERATE_RET ret = pwm_door_control_door(SERVO_ID_DOOR_1, new_door1_state);
                if (ret == OPRT_OK) {
                    door1 = new_door1_state;
                    PR_INFO("✅ 舵机1控制成功: %s", new_door1_state ? "开启" : "关闭");
                } else {
                    PR_ERR("❌ 舵机1控制失败: %d", ret);
                }
            }
            break;

        case DP_DOOR_2: // DP112 - 使用PCA9685控制舵机2 (备用)
            if (dp->type == PROP_BOOL) {
                bool new_door2_state = dp->value.dp_bool ? true : false;
                PR_INFO("📥 收到DP112舱门2控制: %s", new_door2_state ? "开启" : "关闭");

                // 直接使用PCA9685控制舵机
                OPERATE_RET ret = pwm_door_control_door(SERVO_ID_DOOR_2, new_door2_state);
                if (ret == OPRT_OK) {
                    door2 = new_door2_state;
                    PR_INFO("✅ 舵机2控制成功: %s", new_door2_state ? "开启" : "关闭");
                } else {
                    PR_ERR("❌ 舵机2控制失败: %d", ret);
                }
            }
            break;

        default:
            break;
        }
    }
    return OPRT_OK;
}

#include "app_chat_bot.h"
#include "ai_audio.h"
#include "reset_netcfg.h"
#include "app_system_info.h"

/* Tuya device handle */
tuya_iot_client_t ai_client;

#ifndef PROJECT_VERSION
#define PROJECT_VERSION "1.0.0"
#endif

#define DPID_VOLUME 3

static uint8_t _need_reset = 0;

/**
 * @brief user defined log output api, in this demo, it will use uart0 as log-tx
 *
 * @param str log string
 * @return void
 */
void user_log_output_cb(const char *str)
{
    tal_uart_write(TUYA_UART_NUM_0, (const uint8_t *)str, strlen(str));
}

/**
 * @brief user defined upgrade notify callback, it will notify device a OTA request received
 *
 * @param client device info
 * @param upgrade the upgrade request info
 * @return void
 */
void user_upgrade_notify_on(tuya_iot_client_t *client, cJSON *upgrade)
{
    PR_INFO("----- Upgrade information -----");
    PR_INFO("OTA Channel: %d", cJSON_GetObjectItem(upgrade, "type")->valueint);
    PR_INFO("Version: %s", cJSON_GetObjectItem(upgrade, "version")->valuestring);
    PR_INFO("Size: %s", cJSON_GetObjectItem(upgrade, "size")->valuestring);
    PR_INFO("MD5: %s", cJSON_GetObjectItem(upgrade, "md5")->valuestring);
    PR_INFO("HMAC: %s", cJSON_GetObjectItem(upgrade, "hmac")->valuestring);
    PR_INFO("URL: %s", cJSON_GetObjectItem(upgrade, "url")->valuestring);
    PR_INFO("HTTPS URL: %s", cJSON_GetObjectItem(upgrade, "httpsUrl")->valuestring);
}

OPERATE_RET audio_dp_obj_proc(dp_obj_recv_t *dpobj)
{
    uint32_t index = 0;
    for (index = 0; index < dpobj->dpscnt; index++) {
        dp_obj_t *dp = dpobj->dps + index;
        PR_DEBUG("idx:%d dpid:%d type:%d ts:%u", index, dp->id, dp->type, dp->time_stamp);

        switch (dp->id) {
        case DPID_VOLUME: {
            uint8_t volume = dp->value.dp_value;
            PR_DEBUG("volume:%d", volume);
            ai_audio_set_volume(volume);
            char volume_str[20] = {0};
#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
            snprintf(volume_str, sizeof(volume_str), "%s%d", VOLUME, volume);
            app_display_send_msg(TY_DISPLAY_TP_NOTIFICATION, (uint8_t *)volume_str, strlen(volume_str));
#endif
            break;
        }
        default:
            break;
        }
    }

    return OPRT_OK;
}

OPERATE_RET ai_audio_volume_upload(void)
{
    tuya_iot_client_t *client = tuya_iot_client_get();
    dp_obj_t dp_obj = {0};

    uint8_t volume = ai_audio_get_volume();

    dp_obj.id = DPID_VOLUME;
    dp_obj.type = PROP_VALUE;
    dp_obj.value.dp_value = volume;

    PR_DEBUG("DP upload volume:%d", volume);

    return tuya_iot_dp_obj_report(client, client->activate.devid, &dp_obj, 1, 0);
}

/**
 * @brief user defined event handler
 *
 * @param client device info
 * @param event the event info
 * @return void
 */
void user_event_handler_on(tuya_iot_client_t *client, tuya_event_msg_t *event)
{
    PR_DEBUG("Tuya Event ID:%d(%s)", event->id, EVENT_ID2STR(event->id));
    PR_INFO("Device Free heap %d", tal_system_get_free_heap_size());

    switch (event->id) {
        case TUYA_EVENT_BIND_START:
            PR_INFO("Device Bind Start!");
            if (_need_reset == 1) {
                PR_INFO("Device Reset!");
                tal_system_reset();
            }
            break;

        case TUYA_EVENT_BIND_TOKEN_ON:
            break;

        case TUYA_EVENT_ACTIVATE_SUCCESSED:
            break;

        case TUYA_EVENT_MQTT_CONNECTED:
            PR_INFO("Device MQTT Connected!");
            PR_INFO("🚪 智能药盒双门控制系统已就绪 - DP111(P06), DP112(P07)");

            // 初始化PWM门控制系统
            if (pwm_door_control_init() == OPRT_OK) {
                PR_INFO("✅ PWM门控制系统初始化成功");

                // 启动门状态检查循环
                if (start_door_check_loop() == OPRT_OK) {
                    PR_INFO("✅ 门状态检查循环已启动");
                } else {
                    PR_ERR("❌ 门状态检查循环启动失败");
                }

                // 上报初始门状态到云端
                PR_INFO("📤 上报初始门状态到云端...");
                report_door_status(true); // 强制上报初始状态
            } else {
                PR_ERR("❌ PWM门控制系统初始化失败");
            }
            break;

        case TUYA_EVENT_MQTT_DISCONNECT:
            PR_INFO("Device MQTT DisConnected!");
            // 停止门状态检查循环
            stop_door_check_loop();
            break;

        case TUYA_EVENT_TIMESTAMP_SYNC:
            PR_INFO("Time sync: %d", event->value.asInteger);
            tal_time_set_posix(event->value.asInteger, 1);
            break;

        case TUYA_EVENT_RESET:
            PR_INFO("Device Reset:%d", event->value.asInteger);
            _need_reset = 1;
            break;

        case TUYA_EVENT_DP_RECEIVE:
            break;

        case TUYA_EVENT_DP_RECEIVE_CJSON:
            break;

        case TUYA_EVENT_DP_RECEIVE_OBJ: {
            dp_obj_recv_t *dpobj = event->value.dpobj;
            door_dp_obj_proc(dpobj);
            tuya_iot_dp_obj_report(client, dpobj->devid, dpobj->dps, dpobj->dpscnt, 0);
            break;
        }

        case TUYA_EVENT_DP_RECEIVE_RAW: {
            dp_raw_recv_t *dpraw = event->value.dpraw;
            if (dpraw->devid != NULL) {
                PR_DEBUG("devid.%s", dpraw->devid);
            }

            uint32_t index = 0;
            dp_raw_t *dp = &dpraw->dp;
            PR_DEBUG("dpid:%d type:RAW len:%d data:", dp->id, dp->len);
            for (index = 0; index < dp->len; index++) {
                PR_DEBUG_RAW("%02x", dp->data[index]);
            }

            tuya_iot_dp_raw_report(client, dpraw->devid, &dpraw->dp, 3);
            break;
        }

        case TUYA_EVENT_UPGRADE_NOTIFY:
            break;

        case TUYA_EVENT_RESET_COMPLETE:
            break;

        case TUYA_EVENT_DPCACHE_NOTIFY:
            break;

        case TUYA_EVENT_BINDED_NOTIFY:
            break;

        case TUYA_EVENT_DIRECT_MQTT_CONNECTED:
            break;

        default:
            break;
    }
}

/**
 * @brief user defined network check callback, it will check the network every 1sec,
 *        in this demo it alwasy return ture due to it's a wired demo
 *
 * @return true
 * @return false
 */
bool user_network_check(void)
{
    netmgr_status_e status = NETMGR_LINK_DOWN;
    netmgr_conn_get(NETCONN_AUTO, NETCONN_CMD_STATUS, &status);
    return status == NETMGR_LINK_DOWN ? false : true;
}

void user_main(void)
{
    int ret = OPRT_OK;

    //! open iot development kit runtim init
    cJSON_InitHooks(&(cJSON_Hooks){.malloc_fn = tal_malloc, .free_fn = tal_free});
    tal_log_init(TAL_LOG_LEVEL_DEBUG, 1024, (TAL_LOG_OUTPUT_CB)tkl_log_output);

    PR_NOTICE("Application information:");
    PR_NOTICE("Project name:        %s", PROJECT_NAME);
    PR_NOTICE("App version:         %s", PROJECT_VERSION);
    PR_NOTICE("Compile time:        %s", __DATE__);
    PR_NOTICE("TuyaOpen version:    %s", OPEN_VERSION);
    PR_NOTICE("TuyaOpen commit-id:  %s", OPEN_COMMIT);
    PR_NOTICE("Platform chip:       %s", PLATFORM_CHIP);
    PR_NOTICE("Platform board:      %s", PLATFORM_BOARD);
    PR_NOTICE("Platform commit-id:  %s", PLATFORM_COMMIT);

    tal_kv_init(&(tal_kv_cfg_t){
        .seed = "vmlkasdh93dlvlcy",
        .key = "dflfuap134ddlduq",
    });
    tal_sw_timer_init();
    tal_workq_init();
    tal_cli_init();
    tuya_authorize_init();

    reset_netconfig_start();

    tuya_iot_license_t license;

    if (OPRT_OK != tuya_authorize_read(&license)) {
        license.uuid = TUYA_OPENSDK_UUID;
        license.authkey = TUYA_OPENSDK_AUTHKEY;
        PR_WARN("Replace the TUYA_OPENSDK_UUID and TUYA_OPENSDK_AUTHKEY contents, otherwise the demo cannot work.\n \
                Visit https://platform.tuya.com/purchase/index?type=6 to get the open-sdk uuid and authkey.");
    }

    /* Initialize Tuya device configuration */
    ret = tuya_iot_init(&ai_client, &(const tuya_iot_config_t){
                                        .software_ver = PROJECT_VERSION,
                                        .productkey = TUYA_PRODUCT_ID,
                                        .uuid = license.uuid,
                                        .authkey = license.authkey,
                                        // .firmware_key      = TUYA_DEVICE_FIRMWAREKEY,
                                        .event_handler = user_event_handler_on,
                                        .network_check = user_network_check,
                                    });
    assert(ret == OPRT_OK);

    // 初始化LWIP
#if defined(ENABLE_LIBLWIP) && (ENABLE_LIBLWIP == 1)
    TUYA_LwIP_Init();
#endif

    // network init
    netmgr_type_e type = 0;
#if defined(ENABLE_WIFI) && (ENABLE_WIFI == 1)
    type |= NETCONN_WIFI;
#endif
#if defined(ENABLE_WIRED) && (ENABLE_WIRED == 1)
    type |= NETCONN_WIRED;
#endif
    netmgr_init(type);
#if defined(ENABLE_WIFI) && (ENABLE_WIFI == 1)
    netmgr_conn_set(NETCONN_WIFI, NETCONN_CMD_NETCFG, &(netcfg_args_t){.type = NETCFG_TUYA_BLE});
#endif

    PR_DEBUG("tuya_iot_init success");

    ret = board_register_hardware();
    if (ret != OPRT_OK) {
        PR_ERR("board_register_hardware failed");
    }

    ret = app_chat_bot_init();
    if (ret != OPRT_OK) {
        PR_ERR("tuya_audio_recorde_init failed");
    }

#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
    // 初始化显示系统
    ret = app_display_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ 显示系统初始化失败: %d", ret);
    } else {
        PR_INFO("✅ 显示系统初始化成功");

        // 显示系统启动消息
        tal_system_sleep(1000); // 等待1秒让显示系统完全初始化
        app_display_send_msg(TY_DISPLAY_TP_STATUS, (uint8_t *)"智能药盒系统启动", strlen("智能药盒系统启动"));
        PR_INFO("📺 显示系统启动消息已发送");
    }
#endif

    app_system_info();

    // 启动PCA9685舵机控制测试
    PR_INFO("🧪 启动PCA9685舵机控制测试...");
    ret = pca9685_servo_test_start();
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685舵机测试启动失败: %d", ret);
    } else {
        PR_INFO("✅ PCA9685舵机测试启动成功");
    }

    /* Start tuya iot task */
    tuya_iot_start(&ai_client);

    tkl_wifi_set_lp_mode(0, 0);

    reset_netconfig_check();

    for (;;) {
        /* Loop to receive packets, and handles client keepalive */
        tuya_iot_yield(&ai_client);
    }
}

/**
 * @brief main
 *
 * @param argc
 * @param argv
 * @return void
 */
#if OPERATING_SYSTEM == SYSTEM_LINUX
void main(int argc, char *argv[])
{
    user_main();
}
#else

/* Tuya thread handle */
static THREAD_HANDLE ty_app_thread = NULL;

/**
 * @brief  task thread
 *
 * @param[in] arg:Parameters when creating a task
 * @return none
 */
static void tuya_app_thread(void *arg)
{
    user_main();

    tal_thread_delete(ty_app_thread);
    ty_app_thread = NULL;
}

void tuya_app_main(void)
{
    THREAD_CFG_T thrd_param = {4096, 4, "tuya_app_main"};
    tal_thread_create_and_start(&ty_app_thread, NULL, NULL, tuya_app_thread, NULL, &thrd_param);
}
#endif

