/**
 * @file app_button_control.h
 * @brief 按钮控制舵机门的头文件
 * 
 * 提供按钮控制舵机门开关的功能接口
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __APP_BUTTON_CONTROL_H__
#define __APP_BUTTON_CONTROL_H__

#include "tuya_cloud_types.h"
#include "pwm_door_control.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

/**
 * @brief 按钮控制功能开关
 */
#ifndef ENABLE_BUTTON_DOOR_CONTROL
#define ENABLE_BUTTON_DOOR_CONTROL 1
#endif

/**
 * @brief 按钮事件定义
 */
#define BUTTON_EVENT_SINGLE_CLICK   1   /**< 单击 - 原有功能 */
#define BUTTON_EVENT_DOUBLE_CLICK   2   /**< 双击 - 门控制 */
#define BUTTON_EVENT_LONG_PRESS     3   /**< 长按 - 紧急停止 */

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 按钮门控制状态
 */
typedef enum {
    BUTTON_DOOR_STATE_UNKNOWN = 0,  /**< 未知状态 */
    BUTTON_DOOR_STATE_CLOSED,       /**< 门关闭 */
    BUTTON_DOOR_STATE_OPEN,         /**< 门开启 */
    BUTTON_DOOR_STATE_MOVING,       /**< 门运动中 */
    BUTTON_DOOR_STATE_ERROR,        /**< 错误状态 */
    BUTTON_DOOR_STATE_MAX
} button_door_state_e;

/**
 * @brief 按钮控制配置
 */
typedef struct {
    bool enable_door_control;       /**< 是否启用门控制 */
    bool enable_emergency_stop;     /**< 是否启用紧急停止 */
    bool enable_status_display;     /**< 是否启用状态显示 */
    bool enable_audio_feedback;     /**< 是否启用音频反馈 */
    servo_id_e default_servo_id;    /**< 默认舵机ID */
} button_control_config_t;

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 初始化按钮门控制功能
 * 
 * @param config 配置参数
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET app_button_door_control_init(const button_control_config_t *config);

/**
 * @brief 反初始化按钮门控制功能
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET app_button_door_control_deinit(void);

/**
 * @brief 按钮门控制处理函数
 * 
 * @param servo_id 舵机ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET app_button_door_toggle(servo_id_e servo_id);

/**
 * @brief 按钮紧急停止处理函数
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET app_button_emergency_stop(void);

/**
 * @brief 获取当前门状态
 * 
 * @param servo_id 舵机ID
 * @return button_door_state_e 门状态
 */
button_door_state_e app_button_get_door_state(servo_id_e servo_id);

/**
 * @brief 设置门状态
 * 
 * @param servo_id 舵机ID
 * @param state 门状态
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET app_button_set_door_state(servo_id_e servo_id, button_door_state_e state);

/**
 * @brief 获取按钮控制统计信息
 * 
 * @param total_operations 总操作次数
 * @param successful_operations 成功操作次数
 * @param last_operation_time 最后操作时间
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET app_button_get_statistics(uint32_t *total_operations, 
                                     uint32_t *successful_operations, 
                                     uint32_t *last_operation_time);

/**
 * @brief 重置按钮控制统计信息
 *
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET app_button_reset_statistics(void);

/**
 * @brief 运行按钮控制功能完整测试
 */
void app_button_control_run_tests(void);

/**
 * @brief 运行按钮控制功能简单测试
 */
void app_button_control_simple_test(void);

#ifdef __cplusplus
}
#endif

#endif /* __APP_BUTTON_CONTROL_H__ */
