# 按钮控制舵机门使用指南

## 概述

本指南介绍如何使用按钮控制舵机门的开关功能。该功能允许用户通过物理按钮来控制PCA9685驱动的舵机，实现门的开启和关闭操作。

## 功能特性

### 🔘 按钮事件映射

| 按钮操作 | 功能 | 描述 |
|---------|------|------|
| **单击** | 原有功能 | 保持原有的聊天机器人功能 |
| **双击** | 门控制 | 切换门的开启/关闭状态 |
| **长按** | 紧急停止 | 立即停止所有舵机运动 |

### 🚪 门状态管理

- **自动状态跟踪**: 系统自动跟踪每个门的当前状态
- **状态显示**: 在显示屏上实时显示门的状态
- **表情反馈**: 通过表情符号提供视觉反馈
- **错误处理**: 自动处理操作失败的情况

### 📊 统计功能

- **操作计数**: 记录总操作次数和成功次数
- **成功率统计**: 计算操作成功率
- **时间戳**: 记录最后操作时间

## 使用方法

### 基本操作

1. **开启/关闭门**
   - 快速双击按钮
   - 系统会自动切换门的状态
   - 显示屏会显示当前操作和状态

2. **紧急停止**
   - 长按按钮（3秒以上）
   - 所有舵机立即停止运动
   - 显示紧急停止状态

### 状态指示

#### 显示消息
- `门1已开启` - 门成功开启
- `门1已关闭` - 门成功关闭
- `门1运动中...` - 门正在运动
- `门1控制错误` - 操作失败
- `紧急停止已激活` - 紧急停止执行

#### 表情反馈
- 😊 (EMOJI_HAPPY) - 门开启成功
- 😐 (EMOJI_NEUTRAL) - 门关闭成功
- 🤔 (EMOJI_THINKING) - 门运动中
- 😞 (EMOJI_DISAPPOINTED) - 操作失败
- 😨 (EMOJI_FEARFUL) - 紧急停止

## 技术实现

### 系统架构

```
按钮事件 -> 按钮回调 -> 按钮控制模块 -> PWM门控制 -> PCA9685驱动 -> 舵机
                    |
                    v
               显示反馈模块
```

### 关键组件

1. **app_button_control.h/c** - 按钮控制核心模块
2. **pwm_door_control.h/c** - PWM门控制模块
3. **pca9685_driver.h/c** - PCA9685驱动模块
4. **app_display.h/c** - 显示反馈模块

### 配置参数

```c
button_control_config_t config = {
    .enable_door_control = true,     // 启用门控制
    .enable_emergency_stop = true,   // 启用紧急停止
    .enable_status_display = true,   // 启用状态显示
    .enable_audio_feedback = false,  // 音频反馈（暂未实现）
    .default_servo_id = SERVO_ID_DOOR_1  // 默认舵机ID
};
```

## 安全特性

### 🛡️ 安全机制

1. **状态验证**: 每次操作前验证系统状态
2. **错误恢复**: 操作失败时自动恢复到安全状态
3. **紧急停止**: 长按按钮可立即停止所有运动
4. **超时保护**: 防止舵机长时间运行

### ⚠️ 注意事项

1. **按钮防抖**: 系统已配置50ms防抖时间
2. **双击识别**: 双击间隔时间为500ms
3. **长按时间**: 长按触发时间为3秒
4. **状态同步**: 确保硬件状态与软件状态同步

## 测试功能

### 完整测试

```c
// 运行完整测试套件
app_button_control_run_tests();
```

测试内容：
- 初始化测试
- 门状态获取测试
- 单次门控制测试
- 多次门控制测试
- 统计信息测试
- 紧急停止测试

### 简单测试

```c
// 运行简单测试
app_button_control_simple_test();
```

测试内容：
- 基本初始化
- 开门/关门操作
- 统计信息显示

## 故障排除

### 常见问题

1. **按钮无响应**
   - 检查按钮硬件连接
   - 确认ENABLE_BUTTON宏已启用
   - 检查按钮配置参数

2. **舵机不动作**
   - 检查PCA9685连接
   - 确认I2C通信正常
   - 检查舵机电源供应

3. **状态显示异常**
   - 检查显示模块初始化
   - 确认ENABLE_CHAT_DISPLAY宏已启用
   - 检查显示消息格式

### 调试信息

系统会输出详细的调试信息：
- `🔘 按钮控制门1: 关闭 -> 开启`
- `✅ 门1控制成功: 已开启`
- `❌ 门1控制失败: -1`
- `🚨 按钮触发紧急停止!`

## 扩展功能

### 未来改进

1. **音频反馈**: 添加操作音效
2. **多门支持**: 支持控制多个门
3. **定时控制**: 添加定时开关功能
4. **远程控制**: 支持网络远程控制
5. **状态记忆**: 断电后恢复门状态

### 自定义配置

可以通过修改配置参数来自定义功能：
- 调整按钮触发时间
- 修改显示消息内容
- 更改表情反馈映射
- 添加新的安全检查

## 总结

按钮控制舵机门功能提供了一个简单、安全、可靠的物理控制接口。通过双击按钮可以方便地控制门的开关，长按按钮可以在紧急情况下停止所有运动。系统具有完善的状态管理、错误处理和用户反馈机制，确保操作的安全性和可靠性。
