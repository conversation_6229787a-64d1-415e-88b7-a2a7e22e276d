/**
 * @file pca9685_servo_test.h
 * @brief PCA9685舵机控制测试程序头文件
 * 
 * 用于测试PCA9685驱动和MG90S舵机控制功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __PCA9685_SERVO_TEST_H__
#define __PCA9685_SERVO_TEST_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

// ========== 函数声明 ==========

/**
 * @brief 启动PCA9685舵机控制测试
 * 
 * 创建测试任务，执行各种舵机控制测试
 * 
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_servo_test_start(void);

/**
 * @brief 停止PCA9685舵机控制测试
 * 
 * 停止测试任务，清理资源
 * 
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_servo_test_stop(void);

#ifdef __cplusplus
}
#endif

#endif /* __PCA9685_SERVO_TEST_H__ */
