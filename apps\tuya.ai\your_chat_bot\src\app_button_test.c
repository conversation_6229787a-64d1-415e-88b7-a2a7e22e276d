/**
 * @file app_button_test.c
 * @brief 按钮控制功能测试文件
 * 
 * 测试按钮控制舵机门的各种功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "app_button_control.h"
#include "pwm_door_control.h"
#include "tal_api.h"

/***********************************************************
************************macro define************************
***********************************************************/
#define TEST_DELAY_MS           2000    /**< 测试延时 */
#define TEST_SERVO_ID           SERVO_ID_DOOR_1

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 测试按钮控制初始化
 */
static void test_button_control_init(void)
{
    PR_INFO("🧪 测试按钮控制初始化...");
    
    button_control_config_t config = {
        .enable_door_control = true,
        .enable_emergency_stop = true,
        .enable_status_display = true,
        .enable_audio_feedback = false,
        .default_servo_id = TEST_SERVO_ID
    };
    
    OPERATE_RET ret = app_button_door_control_init(&config);
    if (ret == OPRT_OK) {
        PR_INFO("✅ 按钮控制初始化成功");
    } else {
        PR_ERR("❌ 按钮控制初始化失败: %d", ret);
    }
}

/**
 * @brief 测试门状态切换
 */
static void test_door_toggle(void)
{
    PR_INFO("🧪 测试门状态切换...");
    
    // 测试开门
    PR_INFO("📖 测试开门...");
    OPERATE_RET ret = app_button_door_toggle(TEST_SERVO_ID);
    if (ret == OPRT_OK) {
        PR_INFO("✅ 开门测试成功");
    } else {
        PR_ERR("❌ 开门测试失败: %d", ret);
    }
    
    tal_system_sleep(TEST_DELAY_MS);
    
    // 测试关门
    PR_INFO("📖 测试关门...");
    ret = app_button_door_toggle(TEST_SERVO_ID);
    if (ret == OPRT_OK) {
        PR_INFO("✅ 关门测试成功");
    } else {
        PR_ERR("❌ 关门测试失败: %d", ret);
    }
}

/**
 * @brief 测试门状态获取
 */
static void test_door_state(void)
{
    PR_INFO("🧪 测试门状态获取...");
    
    button_door_state_e state = app_button_get_door_state(TEST_SERVO_ID);
    const char *state_str;
    
    switch (state) {
    case BUTTON_DOOR_STATE_CLOSED:
        state_str = "关闭";
        break;
    case BUTTON_DOOR_STATE_OPEN:
        state_str = "开启";
        break;
    case BUTTON_DOOR_STATE_MOVING:
        state_str = "运动中";
        break;
    case BUTTON_DOOR_STATE_ERROR:
        state_str = "错误";
        break;
    default:
        state_str = "未知";
        break;
    }
    
    PR_INFO("📊 当前门状态: %s", state_str);
}

/**
 * @brief 测试紧急停止
 */
static void test_emergency_stop(void)
{
    PR_INFO("🧪 测试紧急停止...");
    
    OPERATE_RET ret = app_button_emergency_stop();
    if (ret == OPRT_OK) {
        PR_INFO("✅ 紧急停止测试成功");
    } else {
        PR_ERR("❌ 紧急停止测试失败: %d", ret);
    }
}

/**
 * @brief 测试统计信息
 */
static void test_statistics(void)
{
    PR_INFO("🧪 测试统计信息...");
    
    uint32_t total_ops, successful_ops, last_time;
    OPERATE_RET ret = app_button_get_statistics(&total_ops, &successful_ops, &last_time);
    
    if (ret == OPRT_OK) {
        PR_INFO("📊 统计信息:");
        PR_INFO("   总操作次数: %d", total_ops);
        PR_INFO("   成功操作次数: %d", successful_ops);
        PR_INFO("   最后操作时间: %d ms", last_time);
        PR_INFO("   成功率: %.1f%%", 
                total_ops > 0 ? (float)successful_ops * 100.0f / total_ops : 0.0f);
    } else {
        PR_ERR("❌ 获取统计信息失败: %d", ret);
    }
}

/**
 * @brief 测试多次门控制操作
 */
static void test_multiple_operations(void)
{
    PR_INFO("🧪 测试多次门控制操作...");
    
    const int test_count = 5;
    
    for (int i = 0; i < test_count; i++) {
        PR_INFO("📖 第%d次操作...", i + 1);
        
        // 切换门状态
        OPERATE_RET ret = app_button_door_toggle(TEST_SERVO_ID);
        if (ret == OPRT_OK) {
            PR_INFO("✅ 第%d次操作成功", i + 1);
        } else {
            PR_ERR("❌ 第%d次操作失败: %d", i + 1, ret);
        }
        
        // 显示当前状态
        test_door_state();
        
        // 延时
        tal_system_sleep(TEST_DELAY_MS);
    }
}

/**
 * @brief 按钮控制功能完整测试
 */
void app_button_control_run_tests(void)
{
    PR_INFO("🚀 开始按钮控制功能测试");
    
    // 首先初始化PWM门控制系统
    PR_INFO("📖 初始化PWM门控制系统...");
    OPERATE_RET ret = pwm_door_control_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ PWM门控制系统初始化失败: %d", ret);
        return;
    }
    
    // 测试1: 初始化
    test_button_control_init();
    tal_system_sleep(1000);
    
    // 测试2: 门状态获取
    test_door_state();
    tal_system_sleep(1000);
    
    // 测试3: 单次门控制
    test_door_toggle();
    tal_system_sleep(1000);
    
    // 测试4: 多次门控制
    test_multiple_operations();
    tal_system_sleep(1000);
    
    // 测试5: 统计信息
    test_statistics();
    tal_system_sleep(1000);
    
    // 测试6: 紧急停止
    test_emergency_stop();
    tal_system_sleep(1000);
    
    // 最终统计
    test_statistics();
    
    PR_INFO("🎉 按钮控制功能测试完成");
}

/**
 * @brief 按钮控制功能简单测试
 */
void app_button_control_simple_test(void)
{
    PR_INFO("🚀 开始按钮控制简单测试");
    
    // 初始化
    test_button_control_init();
    tal_system_sleep(500);
    
    // 测试开关门
    PR_INFO("📖 测试开门...");
    app_button_door_toggle(TEST_SERVO_ID);
    tal_system_sleep(2000);
    
    PR_INFO("📖 测试关门...");
    app_button_door_toggle(TEST_SERVO_ID);
    tal_system_sleep(2000);
    
    // 显示统计
    test_statistics();
    
    PR_INFO("🎉 按钮控制简单测试完成");
}
