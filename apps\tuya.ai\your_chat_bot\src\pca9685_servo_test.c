/**
 * @file pca9685_servo_test.c
 * @brief PCA9685舵机控制测试程序
 * 
 * 用于测试PCA9685驱动和MG90S舵机控制功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "pca9685_driver.h"
#include "pwm_door_control.h"
#include "tal_api.h"
#include "tkl_output.h"

// ========== 测试配置 ==========

/**
 * @brief 测试任务配置
 */
#define TEST_TASK_PRIORITY      THREAD_PRIO_2
#define TEST_TASK_STACK_SIZE    4096
#define TEST_TASK_NAME          "pca9685_test"

/**
 * @brief 测试参数
 */
#define TEST_SERVO_CHANNEL      SERVO_CHANNEL_0     /**< 测试舵机通道 */
#define TEST_ANGLE_MIN          0                   /**< 最小角度 */
#define TEST_ANGLE_MAX          180                 /**< 最大角度 */
#define TEST_ANGLE_STEP         30                  /**< 角度步进 */
#define TEST_DELAY_MS           2000                /**< 测试延时 */

// ========== 全局变量 ==========

/**
 * @brief 测试任务句柄
 */
static THREAD_HANDLE sg_test_task_handle = 0;

/**
 * @brief 测试运行标志
 */
static bool sg_test_running = false;

// ========== 函数声明 ==========

/**
 * @brief 测试任务主函数
 */
static void pca9685_test_task(void *param);

/**
 * @brief 基础功能测试
 */
static void test_basic_functions(void);

/**
 * @brief 舵机角度扫描测试
 */
static void test_servo_angle_sweep(void);

/**
 * @brief 舵机精度测试
 */
static void test_servo_precision(void);

/**
 * @brief 门控制功能测试
 */
static void test_door_control(void);

// ========== 公共函数实现 ==========

OPERATE_RET pca9685_servo_test_start(void)
{
    if (sg_test_running) {
        PR_WARN("⚠️ PCA9685测试已在运行");
        return OPRT_OK;
    }
    
    PR_INFO("🧪 启动PCA9685舵机控制测试...");
    
    // 创建测试任务
    THREAD_CFG_T task_cfg = {
        .priority = TEST_TASK_PRIORITY,
        .stackDepth = TEST_TASK_STACK_SIZE,
        .thrdname = TEST_TASK_NAME
    };
    
    OPERATE_RET ret = tal_thread_create_and_start(&sg_test_task_handle, 
                                                 NULL, NULL, 
                                                 pca9685_test_task, NULL, 
                                                 &task_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 测试任务创建失败: %d", ret);
        return ret;
    }
    
    sg_test_running = true;
    PR_INFO("✅ PCA9685测试任务启动成功");
    
    return OPRT_OK;
}

OPERATE_RET pca9685_servo_test_stop(void)
{
    if (!sg_test_running) {
        PR_WARN("⚠️ PCA9685测试未运行");
        return OPRT_OK;
    }
    
    PR_INFO("🛑 停止PCA9685舵机控制测试...");
    
    sg_test_running = false;
    
    // 等待任务结束
    if (sg_test_task_handle) {
        tal_thread_delete(sg_test_task_handle);
        sg_test_task_handle = 0;
    }
    
    PR_INFO("✅ PCA9685测试任务停止成功");
    
    return OPRT_OK;
}

// ========== 私有函数实现 ==========

static void pca9685_test_task(void *param)
{
    // 避免未使用参数警告
    (void)param;

    PR_INFO("🧪 PCA9685舵机控制测试开始");
    
    // 等待系统稳定
    tal_system_sleep(1000);
    
    // 初始化PWM门控制系统 (包含PCA9685初始化)
    OPERATE_RET ret = pwm_door_control_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ PWM门控制系统初始化失败: %d", ret);
        sg_test_running = false;
        return;
    }
    
    PR_INFO("📋 开始执行测试序列...");
    
    while (sg_test_running) {
        // 测试1: 基础功能测试
        if (sg_test_running) {
            PR_INFO("🔧 测试1: 基础功能测试");
            test_basic_functions();
            tal_system_sleep(2000);
        }
        
        // 测试2: 舵机角度扫描测试
        if (sg_test_running) {
            PR_INFO("🔄 测试2: 舵机角度扫描测试");
            test_servo_angle_sweep();
            tal_system_sleep(2000);
        }
        
        // 测试3: 舵机精度测试
        if (sg_test_running) {
            PR_INFO("🎯 测试3: 舵机精度测试");
            test_servo_precision();
            tal_system_sleep(2000);
        }
        
        // 测试4: 门控制功能测试
        if (sg_test_running) {
            PR_INFO("🚪 测试4: 门控制功能测试");
            test_door_control();
            tal_system_sleep(5000);
        }
        
        PR_INFO("🔄 测试循环完成，等待下一轮...");
        tal_system_sleep(10000); // 10秒后重复测试
    }
    
    // 清理资源
    pwm_door_control_cleanup();
    
    PR_INFO("✅ PCA9685舵机控制测试结束");
    sg_test_running = false;
}

static void test_basic_functions(void)
{
    PR_INFO("  📊 检查PCA9685设备状态...");
    
    const pca9685_device_t *device = pca9685_get_device_status();
    if (device && device->initialized) {
        PR_INFO("  ✅ PCA9685设备正常: 地址=0x%02X, 频率=%dHz", 
                device->device_addr, device->pwm_freq);
    } else {
        PR_ERR("  ❌ PCA9685设备状态异常");
        return;
    }
    
    // 测试基本角度设置
    uint16_t test_angles[] = {0, 45, 90, 135, 180};
    int angle_count = sizeof(test_angles) / sizeof(test_angles[0]);
    
    for (int i = 0; i < angle_count; i++) {
        uint16_t angle = test_angles[i];
        PR_INFO("  🎛️ 设置角度: %d°", angle);
        
        OPERATE_RET ret = pca9685_set_servo_angle(TEST_SERVO_CHANNEL, angle);
        if (ret == OPRT_OK) {
            PR_INFO("  ✅ 角度%d°设置成功", angle);
        } else {
            PR_ERR("  ❌ 角度%d°设置失败: %d", angle, ret);
        }
        
        tal_system_sleep(1000);
    }
}

static void test_servo_angle_sweep(void)
{
    PR_INFO("  🔄 开始角度扫描: %d° → %d° (步进%d°)", 
            TEST_ANGLE_MIN, TEST_ANGLE_MAX, TEST_ANGLE_STEP);
    
    // 正向扫描
    for (uint16_t angle = TEST_ANGLE_MIN; angle <= TEST_ANGLE_MAX; angle += TEST_ANGLE_STEP) {
        if (!sg_test_running) break;
        
        PR_INFO("  ➡️ 角度: %d°", angle);
        pca9685_set_servo_angle(TEST_SERVO_CHANNEL, angle);
        tal_system_sleep(TEST_DELAY_MS);
    }
    
    // 反向扫描 - 使用不同的循环方式避免编译警告
    for (int16_t angle = TEST_ANGLE_MAX; angle >= TEST_ANGLE_MIN; angle -= TEST_ANGLE_STEP) {
        if (!sg_test_running) break;

        PR_INFO("  ⬅️ 角度: %d°", angle);
        pca9685_set_servo_angle(TEST_SERVO_CHANNEL, (uint16_t)angle);
        tal_system_sleep(TEST_DELAY_MS);
    }
    
    PR_INFO("  ✅ 角度扫描完成");
}

static void test_servo_precision(void)
{
    PR_INFO("  🎯 测试舵机精度控制...");
    
    // 测试精确角度
    uint16_t precise_angles[] = {15, 30, 60, 120, 150, 165};
    int angle_count = sizeof(precise_angles) / sizeof(precise_angles[0]);
    
    for (int i = 0; i < angle_count; i++) {
        uint16_t angle = precise_angles[i];
        
        PR_INFO("  📐 精确角度: %d°", angle);
        
        // 计算预期脉宽
        uint16_t expected_pulse = pca9685_angle_to_pulse_width(angle);
        uint16_t expected_pwm = pca9685_pulse_width_to_pwm(expected_pulse);
        
        PR_INFO("  📊 预期脉宽: %dμs, PWM值: %d", expected_pulse, expected_pwm);
        
        OPERATE_RET ret = pca9685_set_servo_angle(TEST_SERVO_CHANNEL, angle);
        if (ret == OPRT_OK) {
            PR_INFO("  ✅ 精确角度%d°设置成功", angle);
        } else {
            PR_ERR("  ❌ 精确角度%d°设置失败: %d", angle, ret);
        }
        
        tal_system_sleep(1500);
    }
    
    PR_INFO("  ✅ 精度测试完成");
}

static void test_door_control(void)
{
    PR_INFO("  🚪 测试门控制功能...");
    
    // 测试门开关控制
    for (int cycle = 0; cycle < 3; cycle++) {
        if (!sg_test_running) break;
        
        PR_INFO("  🔄 门控制循环 %d/3", cycle + 1);
        
        // 关门
        PR_INFO("  🔒 关门测试...");
        OPERATE_RET ret = pwm_door_control_door(SERVO_ID_DOOR_1, false);
        if (ret == OPRT_OK) {
            PR_INFO("  ✅ 关门成功");
        } else {
            PR_ERR("  ❌ 关门失败: %d", ret);
        }
        tal_system_sleep(3000);
        
        // 开门
        PR_INFO("  🔓 开门测试...");
        ret = pwm_door_control_door(SERVO_ID_DOOR_1, true);
        if (ret == OPRT_OK) {
            PR_INFO("  ✅ 开门成功");
        } else {
            PR_ERR("  ❌ 开门失败: %d", ret);
        }
        tal_system_sleep(3000);
    }
    
    // 回到中心位置
    PR_INFO("  🎯 回到中心位置...");
    pwm_door_control_set_angle(SERVO_ID_DOOR_1, 90);
    
    PR_INFO("  ✅ 门控制测试完成");
}
