/**
 * @file app_button_control.c
 * @brief 按钮控制舵机门的实现文件
 * 
 * 实现按钮控制舵机门开关的功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "app_button_control.h"
#include "app_display.h"
#include "tal_api.h"
#include <string.h>
#include <stdio.h>

/***********************************************************
************************macro define************************
***********************************************************/
#define BUTTON_CONTROL_MAX_SERVOS   2   /**< 最大支持舵机数量 */

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 按钮控制系统状态
 */
typedef struct {
    bool is_initialized;                                    /**< 是否已初始化 */
    button_control_config_t config;                         /**< 配置参数 */
    button_door_state_e door_states[BUTTON_CONTROL_MAX_SERVOS]; /**< 门状态数组 */
    uint32_t total_operations;                              /**< 总操作次数 */
    uint32_t successful_operations;                         /**< 成功操作次数 */
    uint32_t last_operation_time;                           /**< 最后操作时间 */
} button_control_system_t;

/***********************************************************
***********************variable define**********************
***********************************************************/
static button_control_system_t g_button_control_system = {0};

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 显示门状态信息
 */
static void __display_door_status(servo_id_e servo_id, button_door_state_e state)
{
    if (!g_button_control_system.config.enable_status_display) {
        return;
    }

    char status_msg[64];
    const char *emoji;
    
    switch (state) {
    case BUTTON_DOOR_STATE_CLOSED:
        snprintf(status_msg, sizeof(status_msg), "门%d已关闭", servo_id + 1);
        emoji = EMOJI_NEUTRAL;
        break;
    case BUTTON_DOOR_STATE_OPEN:
        snprintf(status_msg, sizeof(status_msg), "门%d已开启", servo_id + 1);
        emoji = EMOJI_HAPPY;
        break;
    case BUTTON_DOOR_STATE_MOVING:
        snprintf(status_msg, sizeof(status_msg), "门%d运动中...", servo_id + 1);
        emoji = EMOJI_THINKING;
        break;
    case BUTTON_DOOR_STATE_ERROR:
        snprintf(status_msg, sizeof(status_msg), "门%d控制错误", servo_id + 1);
        emoji = EMOJI_DISAPPOINTED;
        break;
    default:
        snprintf(status_msg, sizeof(status_msg), "门%d状态未知", servo_id + 1);
        emoji = EMOJI_CONFUSED;
        break;
    }
    
    app_display_send_msg(TY_DISPLAY_TP_SYSTEM_MSG, (uint8_t*)status_msg, strlen(status_msg));
    app_display_send_msg(TY_DISPLAY_TP_EMOTION, (uint8_t*)emoji, strlen(emoji));
}

OPERATE_RET app_button_door_control_init(const button_control_config_t *config)
{
    if (!config) {
        PR_ERR("❌ 按钮控制初始化失败: 配置参数为空");
        return OPRT_INVALID_PARM;
    }
    
    if (g_button_control_system.is_initialized) {
        PR_WARN("⚠️ 按钮控制已经初始化");
        return OPRT_OK;
    }
    
    // 复制配置
    memcpy(&g_button_control_system.config, config, sizeof(button_control_config_t));
    
    // 初始化门状态
    for (int i = 0; i < BUTTON_CONTROL_MAX_SERVOS; i++) {
        g_button_control_system.door_states[i] = BUTTON_DOOR_STATE_CLOSED;
    }
    
    // 重置统计信息
    g_button_control_system.total_operations = 0;
    g_button_control_system.successful_operations = 0;
    g_button_control_system.last_operation_time = 0;
    
    g_button_control_system.is_initialized = true;
    
    PR_INFO("✅ 按钮门控制初始化成功");
    return OPRT_OK;
}

OPERATE_RET app_button_door_control_deinit(void)
{
    if (!g_button_control_system.is_initialized) {
        return OPRT_OK;
    }
    
    memset(&g_button_control_system, 0, sizeof(button_control_system_t));
    
    PR_INFO("✅ 按钮门控制反初始化完成");
    return OPRT_OK;
}

OPERATE_RET app_button_door_toggle(servo_id_e servo_id)
{
    if (!g_button_control_system.is_initialized) {
        PR_ERR("❌ 按钮控制未初始化");
        return OPRT_COM_ERROR;
    }
    
    if (!g_button_control_system.config.enable_door_control) {
        PR_WARN("⚠️ 门控制功能未启用");
        return OPRT_COM_ERROR;
    }
    
    if (servo_id >= BUTTON_CONTROL_MAX_SERVOS) {
        PR_ERR("❌ 无效的舵机ID: %d", servo_id);
        return OPRT_INVALID_PARM;
    }
    
    // 更新统计信息
    g_button_control_system.total_operations++;
    g_button_control_system.last_operation_time = tal_system_get_millisecond();
    
    // 获取当前状态
    button_door_state_e current_state = g_button_control_system.door_states[servo_id];
    bool target_open = (current_state != BUTTON_DOOR_STATE_OPEN);
    
    PR_INFO("🔘 按钮控制门%d: %s -> %s", 
            servo_id + 1,
            (current_state == BUTTON_DOOR_STATE_OPEN) ? "开启" : "关闭",
            target_open ? "开启" : "关闭");
    
    // 设置运动状态
    g_button_control_system.door_states[servo_id] = BUTTON_DOOR_STATE_MOVING;
    __display_door_status(servo_id, BUTTON_DOOR_STATE_MOVING);
    
    // 控制舵机
    OPERATE_RET ret = pwm_door_control_door(servo_id, target_open);
    if (ret == OPRT_OK) {
        // 更新状态
        g_button_control_system.door_states[servo_id] = target_open ? 
            BUTTON_DOOR_STATE_OPEN : BUTTON_DOOR_STATE_CLOSED;
        g_button_control_system.successful_operations++;
        
        __display_door_status(servo_id, g_button_control_system.door_states[servo_id]);
        
        PR_INFO("✅ 门%d控制成功: %s", servo_id + 1, target_open ? "已开启" : "已关闭");
    } else {
        // 错误处理
        g_button_control_system.door_states[servo_id] = BUTTON_DOOR_STATE_ERROR;
        __display_door_status(servo_id, BUTTON_DOOR_STATE_ERROR);
        
        PR_ERR("❌ 门%d控制失败: %d", servo_id + 1, ret);
    }
    
    return ret;
}

OPERATE_RET app_button_emergency_stop(void)
{
    if (!g_button_control_system.is_initialized) {
        PR_ERR("❌ 按钮控制未初始化");
        return OPRT_COM_ERROR;
    }
    
    if (!g_button_control_system.config.enable_emergency_stop) {
        PR_WARN("⚠️ 紧急停止功能未启用");
        return OPRT_COM_ERROR;
    }
    
    PR_WARN("🚨 按钮触发紧急停止!");
    
    // 更新统计信息
    g_button_control_system.total_operations++;
    g_button_control_system.last_operation_time = tal_system_get_millisecond();
    
    // 执行紧急停止
    pwm_door_control_emergency_stop();
    
    // 更新所有门状态为错误状态
    for (int i = 0; i < BUTTON_CONTROL_MAX_SERVOS; i++) {
        g_button_control_system.door_states[i] = BUTTON_DOOR_STATE_ERROR;
    }
    
    // 显示紧急停止状态
    if (g_button_control_system.config.enable_status_display) {
        const char *emergency_msg = "紧急停止已激活";
        app_display_send_msg(TY_DISPLAY_TP_SYSTEM_MSG, (uint8_t*)emergency_msg, strlen(emergency_msg));
        app_display_send_msg(TY_DISPLAY_TP_EMOTION, (uint8_t*)EMOJI_FEARFUL, strlen(EMOJI_FEARFUL));
    }
    
    g_button_control_system.successful_operations++;
    
    PR_INFO("✅ 紧急停止执行完成");
    return OPRT_OK;
}

button_door_state_e app_button_get_door_state(servo_id_e servo_id)
{
    if (!g_button_control_system.is_initialized || servo_id >= BUTTON_CONTROL_MAX_SERVOS) {
        return BUTTON_DOOR_STATE_UNKNOWN;
    }
    
    return g_button_control_system.door_states[servo_id];
}

OPERATE_RET app_button_set_door_state(servo_id_e servo_id, button_door_state_e state)
{
    if (!g_button_control_system.is_initialized) {
        return OPRT_COM_ERROR;
    }
    
    if (servo_id >= BUTTON_CONTROL_MAX_SERVOS || state >= BUTTON_DOOR_STATE_MAX) {
        return OPRT_INVALID_PARM;
    }
    
    g_button_control_system.door_states[servo_id] = state;
    return OPRT_OK;
}

OPERATE_RET app_button_get_statistics(uint32_t *total_operations, 
                                     uint32_t *successful_operations, 
                                     uint32_t *last_operation_time)
{
    if (!g_button_control_system.is_initialized) {
        return OPRT_COM_ERROR;
    }
    
    if (total_operations) {
        *total_operations = g_button_control_system.total_operations;
    }
    
    if (successful_operations) {
        *successful_operations = g_button_control_system.successful_operations;
    }
    
    if (last_operation_time) {
        *last_operation_time = g_button_control_system.last_operation_time;
    }
    
    return OPRT_OK;
}

OPERATE_RET app_button_reset_statistics(void)
{
    if (!g_button_control_system.is_initialized) {
        return OPRT_COM_ERROR;
    }
    
    g_button_control_system.total_operations = 0;
    g_button_control_system.successful_operations = 0;
    g_button_control_system.last_operation_time = 0;
    
    PR_INFO("✅ 按钮控制统计信息已重置");
    return OPRT_OK;
}
