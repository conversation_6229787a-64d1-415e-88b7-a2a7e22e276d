/**
 * @file pca9685_driver.c
 * @brief PCA9685 16通道PWM驱动器实现文件
 * 
 * 基于TuyaOS I2C接口实现PCA9685驱动，用于控制MG90S舵机
 * 参考：https://gitee.com/qi-zezhong/pca9685-stm32
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "pca9685_driver.h"
#include "pwm_door_control.h"
#include "tkl_i2c.h"
#include "tkl_pinmux.h"
#include "tal_log.h"
#include "tal_system.h"
#include <string.h>

// ========== 私有变量 ==========

/**
 * @brief PCA9685设备实例
 */
static pca9685_device_t g_pca9685_device = {0};

// ========== 私有函数声明 ==========

/**
 * @brief 写入PCA9685寄存器
 */
static OPERATE_RET pca9685_write_reg(uint8_t reg_addr, uint8_t data);

/**
 * @brief 读取PCA9685寄存器
 */
static OPERATE_RET pca9685_read_reg(uint8_t reg_addr, uint8_t *data);

/**
 * @brief 计算预分频值
 */
static uint8_t pca9685_calculate_prescale(uint16_t freq);

/**
 * @brief 配置I2C引脚
 */
static OPERATE_RET pca9685_config_i2c_pins(void);

// ========== 公共函数实现 ==========

OPERATE_RET pca9685_init(void)
{
    PR_INFO("🔧 初始化PCA9685驱动...");
    
    // 清空设备状态
    memset(&g_pca9685_device, 0, sizeof(pca9685_device_t));
    
    // 配置I2C引脚
    OPERATE_RET ret = pca9685_config_i2c_pins();
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685 I2C引脚配置失败: %d", ret);
        return ret;
    }
    
    // 初始化I2C
    TUYA_IIC_BASE_CFG_T i2c_cfg = {
        .role = TUYA_IIC_MODE_MASTER,
        .speed = TUYA_IIC_BUS_SPEED_100K,
        .addr_width = TUYA_IIC_ADDRESS_7BIT
    };
    
    ret = tkl_i2c_init(PCA9685_I2C_PORT, &i2c_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685 I2C初始化失败: %d", ret);
        return ret;
    }
    
    // 设置设备参数
    g_pca9685_device.device_addr = PCA9685_I2C_ADDR;
    g_pca9685_device.i2c_port = PCA9685_I2C_PORT;
    g_pca9685_device.pwm_freq = PCA9685_PWM_FREQ;
    
    // 复位PCA9685
    ret = pca9685_write_reg(PCA9685_MODE1, PCA9685_MODE1_RESTART);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685复位失败: %d", ret);
        return ret;
    }
    
    tal_system_sleep(10); // 等待复位完成
    
    // 设置为正常模式，启用自动递增
    ret = pca9685_write_reg(PCA9685_MODE1, PCA9685_MODE1_AI);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685模式设置失败: %d", ret);
        return ret;
    }
    
    // 设置输出驱动器为推挽模式
    ret = pca9685_write_reg(PCA9685_MODE2, PCA9685_MODE2_OUTDRV);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685输出模式设置失败: %d", ret);
        return ret;
    }
    
    // 设置PWM频率
    ret = pca9685_set_pwm_freq(PCA9685_PWM_FREQ);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685频率设置失败: %d", ret);
        return ret;
    }
    
    // 停止所有通道输出
    ret = pca9685_stop_all_channels();
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685通道停止失败: %d", ret);
        return ret;
    }
    
    g_pca9685_device.initialized = true;
    
    PR_INFO("✅ PCA9685驱动初始化完成");
    PR_INFO("📊 PCA9685配置:");
    PR_INFO("   - I2C地址: 0x%02X", g_pca9685_device.device_addr);
    PR_INFO("   - I2C端口: %d", g_pca9685_device.i2c_port);
    PR_INFO("   - PWM频率: %d Hz", g_pca9685_device.pwm_freq);
    PR_INFO("   - 预分频值: %d", g_pca9685_device.prescale_value);
    PR_INFO("   - SDA引脚: P%02d", PCA9685_I2C_SDA_PIN);
    PR_INFO("   - SCL引脚: P%02d", PCA9685_I2C_SCL_PIN);
    
    return OPRT_OK;
}

OPERATE_RET pca9685_deinit(void)
{
    if (!g_pca9685_device.initialized) {
        return OPRT_OK;
    }
    
    PR_INFO("🔄 反初始化PCA9685驱动...");
    
    // 停止所有通道输出
    pca9685_stop_all_channels();
    
    // 设置为睡眠模式
    pca9685_write_reg(PCA9685_MODE1, PCA9685_MODE1_SLEEP);
    
    // 反初始化I2C
    tkl_i2c_deinit(g_pca9685_device.i2c_port);
    
    // 清空设备状态
    memset(&g_pca9685_device, 0, sizeof(pca9685_device_t));
    
    PR_INFO("✅ PCA9685驱动反初始化完成");
    return OPRT_OK;
}

OPERATE_RET pca9685_set_pwm_freq(uint16_t freq)
{
    if (!g_pca9685_device.initialized) {
        PR_ERR("❌ PCA9685未初始化");
        return OPRT_COM_ERROR;
    }
    
    if (freq < 24 || freq > 1526) {
        PR_ERR("❌ PWM频率超出范围: %d (24-1526 Hz)", freq);
        return OPRT_INVALID_PARM;
    }
    
    // 计算预分频值
    uint8_t prescale = pca9685_calculate_prescale(freq);
    g_pca9685_device.prescale_value = prescale;
    
    PR_DEBUG("🎛️ 设置PCA9685频率: %d Hz (预分频: %d)", freq, prescale);
    
    // 读取当前模式1寄存器
    uint8_t old_mode;
    OPERATE_RET ret = pca9685_read_reg(PCA9685_MODE1, &old_mode);
    if (ret != OPRT_OK) {
        return ret;
    }
    
    // 进入睡眠模式以设置预分频
    uint8_t new_mode = (old_mode & 0x7F) | PCA9685_MODE1_SLEEP;
    ret = pca9685_write_reg(PCA9685_MODE1, new_mode);
    if (ret != OPRT_OK) {
        return ret;
    }
    
    // 设置预分频值
    ret = pca9685_write_reg(PCA9685_PRESCALE, prescale);
    if (ret != OPRT_OK) {
        return ret;
    }
    
    // 恢复原模式
    ret = pca9685_write_reg(PCA9685_MODE1, old_mode);
    if (ret != OPRT_OK) {
        return ret;
    }
    
    tal_system_sleep(5); // 等待稳定
    
    // 重启以应用新频率
    ret = pca9685_write_reg(PCA9685_MODE1, old_mode | PCA9685_MODE1_RESTART);
    if (ret != OPRT_OK) {
        return ret;
    }
    
    g_pca9685_device.pwm_freq = freq;
    
    PR_DEBUG("✅ PCA9685频率设置完成: %d Hz", freq);
    return OPRT_OK;
}

OPERATE_RET pca9685_set_pwm(uint8_t channel, uint16_t on_time, uint16_t off_time)
{
    if (!g_pca9685_device.initialized) {
        PR_ERR("❌ PCA9685未初始化");
        return OPRT_COM_ERROR;
    }
    
    if (channel >= SERVO_CHANNEL_MAX) {
        PR_ERR("❌ 通道号超出范围: %d (最大15)", channel);
        return OPRT_INVALID_PARM;
    }
    
    if (on_time >= PCA9685_PWM_RESOLUTION || off_time >= PCA9685_PWM_RESOLUTION) {
        PR_ERR("❌ PWM值超出范围: on=%d, off=%d (最大4095)", on_time, off_time);
        return OPRT_INVALID_PARM;
    }
    
    // 计算寄存器地址
    uint8_t reg_base = PCA9685_LED0_ON_L + 4 * channel;
    
    // 写入PWM值
    OPERATE_RET ret;
    ret = pca9685_write_reg(reg_base, on_time & 0xFF);
    if (ret != OPRT_OK) return ret;
    
    ret = pca9685_write_reg(reg_base + 1, on_time >> 8);
    if (ret != OPRT_OK) return ret;
    
    ret = pca9685_write_reg(reg_base + 2, off_time & 0xFF);
    if (ret != OPRT_OK) return ret;
    
    ret = pca9685_write_reg(reg_base + 3, off_time >> 8);
    if (ret != OPRT_OK) return ret;
    
    PR_DEBUG("🎛️ 通道%d PWM设置: ON=%d, OFF=%d", channel, on_time, off_time);
    return OPRT_OK;
}

OPERATE_RET pca9685_set_servo_angle(uint8_t channel, uint16_t angle)
{
    if (angle > 180) {
        PR_ERR("❌ 舵机角度超出范围: %d (最大180度)", angle);
        return OPRT_INVALID_PARM;
    }
    
    // 角度转换为脉宽
    uint16_t pulse_width = pca9685_angle_to_pulse_width(angle);
    
    PR_INFO("🎛️ 设置舵机通道%d角度: %d° → %dμs", channel, angle, pulse_width);
    
    return pca9685_set_servo_pulse_width(channel, pulse_width);
}

OPERATE_RET pca9685_set_servo_pulse_width(uint8_t channel, uint16_t pulse_width)
{
    if (pulse_width < MG90S_MIN_PULSE_WIDTH || pulse_width > MG90S_MAX_PULSE_WIDTH) {
        PR_ERR("❌ 舵机脉宽超出范围: %d (%d-%d μs)", 
               pulse_width, MG90S_MIN_PULSE_WIDTH, MG90S_MAX_PULSE_WIDTH);
        return OPRT_INVALID_PARM;
    }
    
    // 脉宽转换为PWM值
    uint16_t pwm_value = pca9685_pulse_width_to_pwm(pulse_width);
    
    PR_DEBUG("🎯 通道%d脉宽设置: %dμs → PWM值%d", channel, pulse_width, pwm_value);
    
    return pca9685_set_pwm(channel, 0, pwm_value);
}

OPERATE_RET pca9685_stop_channel(uint8_t channel)
{
    if (channel >= SERVO_CHANNEL_MAX) {
        PR_ERR("❌ 通道号超出范围: %d", channel);
        return OPRT_INVALID_PARM;
    }
    
    return pca9685_set_pwm(channel, 0, 0);
}

OPERATE_RET pca9685_stop_all_channels(void)
{
    OPERATE_RET ret;
    
    ret = pca9685_write_reg(PCA9685_ALL_LED_ON_L, 0);
    if (ret != OPRT_OK) return ret;
    
    ret = pca9685_write_reg(PCA9685_ALL_LED_ON_H, 0);
    if (ret != OPRT_OK) return ret;
    
    ret = pca9685_write_reg(PCA9685_ALL_LED_OFF_L, 0);
    if (ret != OPRT_OK) return ret;
    
    ret = pca9685_write_reg(PCA9685_ALL_LED_OFF_H, 0);
    if (ret != OPRT_OK) return ret;
    
    PR_DEBUG("🛑 所有PCA9685通道已停止");
    return OPRT_OK;
}

const pca9685_device_t* pca9685_get_device_status(void)
{
    return &g_pca9685_device;
}

uint16_t pca9685_angle_to_pulse_width(uint16_t angle)
{
    if (angle > 180) {
        angle = 180;
    }

    // 线性插值计算脉宽
    // 0度 -> 500μs, 180度 -> 2500μs
    uint16_t pulse_width = MG90S_MIN_PULSE_WIDTH +
                          (angle * (MG90S_MAX_PULSE_WIDTH - MG90S_MIN_PULSE_WIDTH)) / 180;

    return pulse_width;
}

uint16_t pca9685_pulse_width_to_pwm(uint16_t pulse_width)
{
    // PWM周期 = 1/频率 = 1/50Hz = 20ms = 20000μs
    // PWM值 = (脉宽 / 周期) * 4096
    uint32_t pwm_period_us = 1000000 / g_pca9685_device.pwm_freq;
    uint32_t pwm_value = (pulse_width * PCA9685_PWM_RESOLUTION) / pwm_period_us;

    if (pwm_value >= PCA9685_PWM_RESOLUTION) {
        pwm_value = PCA9685_PWM_RESOLUTION - 1;
    }

    return (uint16_t)pwm_value;
}

// ========== 私有函数实现 ==========

static OPERATE_RET pca9685_write_reg(uint8_t reg_addr, uint8_t data)
{
    uint8_t write_data[2] = {reg_addr, data};

    OPERATE_RET ret = tkl_i2c_master_send(g_pca9685_device.i2c_port,
                                         g_pca9685_device.device_addr,
                                         write_data, 2, FALSE);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685写寄存器失败: 0x%02X = 0x%02X, 错误: %d",
               reg_addr, data, ret);
        return ret;
    }

    return OPRT_OK;
}

static OPERATE_RET pca9685_read_reg(uint8_t reg_addr, uint8_t *data)
{
    if (!data) {
        return OPRT_INVALID_PARM;
    }

    // 发送寄存器地址
    OPERATE_RET ret = tkl_i2c_master_send(g_pca9685_device.i2c_port,
                                         g_pca9685_device.device_addr,
                                         &reg_addr, 1, TRUE);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685发送寄存器地址失败: 0x%02X, 错误: %d", reg_addr, ret);
        return ret;
    }

    // 读取数据
    ret = tkl_i2c_master_receive(g_pca9685_device.i2c_port,
                                g_pca9685_device.device_addr,
                                data, 1, FALSE);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PCA9685读寄存器失败: 0x%02X, 错误: %d", reg_addr, ret);
        return ret;
    }

    return OPRT_OK;
}

static uint8_t pca9685_calculate_prescale(uint16_t freq)
{
    // 预分频值计算公式: prescale = round(osc_clock / (4096 * freq)) - 1
    // 其中 osc_clock = 25MHz
    uint32_t prescale_value = (PCA9685_INTERNAL_FREQ / (4096 * freq)) - 1;

    // 四舍五入
    prescale_value = (prescale_value + 1) / 2 * 2;

    // 限制范围 (3-255)
    if (prescale_value < 3) {
        prescale_value = 3;
    } else if (prescale_value > 255) {
        prescale_value = 255;
    }

    return (uint8_t)prescale_value;
}

static OPERATE_RET pca9685_config_i2c_pins(void)
{
    PR_DEBUG("🔧 配置PCA9685 I2C引脚...");

    // 配置SCL引脚 (P07)
    OPERATE_RET ret = tkl_io_pinmux_config(PCA9685_I2C_SCL_PIN, TUYA_IIC0_SCL);
    if (ret != OPRT_OK) {
        PR_ERR("❌ SCL引脚配置失败: P%02d, 错误: %d", PCA9685_I2C_SCL_PIN, ret);
        return ret;
    }

    // 配置SDA引脚 (P06)
    ret = tkl_io_pinmux_config(PCA9685_I2C_SDA_PIN, TUYA_IIC0_SDA);
    if (ret != OPRT_OK) {
        PR_ERR("❌ SDA引脚配置失败: P%02d, 错误: %d", PCA9685_I2C_SDA_PIN, ret);
        return ret;
    }

    PR_DEBUG("✅ I2C引脚配置完成: SCL=P%02d, SDA=P%02d",
             PCA9685_I2C_SCL_PIN, PCA9685_I2C_SDA_PIN);

    return OPRT_OK;
}
