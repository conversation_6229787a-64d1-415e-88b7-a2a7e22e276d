/**
 * @file pwm_door_control.h
 * @brief PWM舵机门控制模块头文件
 * 
 * 提供基于PWM的舵机门控制功能，支持DP111和DP112控制
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __PWM_DOOR_CONTROL_H__
#define __PWM_DOOR_CONTROL_H__

#include "tuya_cloud_types.h"
#include "tkl_pwm.h"

#ifdef __cplusplus
extern "C" {
#endif

// ========== PWM舵机控制常量定义 ==========

/**
 * @brief PWM连续旋转舵机控制参数 - 基于占空比表格
 */
#define SERVO_PWM_FREQ              50          /**< PWM频率 50Hz */
#define SERVO_PWM_CYCLE             20000       /**< PWM周期 20ms (20000μs) */

/**
 * @brief 连续旋转舵机占空比定义 - 基于用户提供的表格
 *
 * PWM占空比 | 舵机状态
 * ---------|----------
 * 0%       | 停止
 * 2.5%     | 顺时钟快转
 * 5%       | 顺时钟慢转
 * 7.5%     | 停止
 * 10%      | 逆时针慢转
 * 12.5%    | 逆时针快转
 * 100%     | 停止
 */
#define SERVO_DUTY_STOP_0           0           /**< 停止状态 - 0%占空比 */
#define SERVO_DUTY_CW_FAST          500         /**< 顺时钟快转 - 2.5%占空比 (500μs) */
#define SERVO_DUTY_CW_SLOW          1000        /**< 顺时钟慢转 - 5%占空比 (1000μs) */
#define SERVO_DUTY_STOP_75          1500        /**< 停止状态 - 7.5%占空比 (1500μs) */
#define SERVO_DUTY_CCW_SLOW         2000        /**< 逆时针慢转 - 10%占空比 (2000μs) */
#define SERVO_DUTY_CCW_FAST         2500        /**< 逆时针快转 - 12.5%占空比 (2500μs) */
#define SERVO_DUTY_STOP_100         20000       /**< 停止状态 - 100%占空比 (20000μs) */

/**
 * @brief 门控制专用占空比定义
 */
#define SERVO_DOOR_OPEN_DUTY        SERVO_DUTY_CW_SLOW   /**< 门开启 - 顺时钟慢转 */
#define SERVO_DOOR_CLOSE_DUTY       SERVO_DUTY_CCW_SLOW  /**< 门关闭 - 逆时针慢转 */
#define SERVO_DOOR_STOP_DUTY        SERVO_DUTY_STOP_75   /**< 门停止 - 7.5%占空比 */

/**
 * @brief 角度控制舵机PWM占空比定义 (用于MG90S等角度舵机)
 *
 * 标准舵机控制信号：
 * - 频率: 50Hz (周期20ms)
 * - 0度: 0.5ms脉宽 (500μs)
 * - 180度: 2.5ms脉宽 (2500μs)
 */
#define SERVO_PWM_DUTY_0_DEG        500         /**< 0度位置占空比 (500μs) */
#define SERVO_PWM_DUTY_90_DEG       1500        /**< 90度位置占空比 (1500μs) */
#define SERVO_PWM_DUTY_180_DEG      2500        /**< 180度位置占空比 (2500μs) */

/**
 * @brief DP点定义 - 根据智能药盒DP配置
 */
#define DP_DOOR_1                   111         /**< 1号门开关DP (P06) - door1 */
#define DP_DOOR_2                   112         /**< 2号门开关DP (P07) - door2 */

/**
 * @brief GPIO引脚定义 - PCA9685 I2C舵机控制
 */
#define PCA9685_I2C_SDA_PIN         TUYA_GPIO_NUM_6     /**< P06引脚 - PCA9685 SDA */
#define PCA9685_I2C_SCL_PIN         TUYA_GPIO_NUM_7     /**< P07引脚 - PCA9685 SCL */

/**
 * @brief 舵机GPIO引脚映射表 (用于兼容性)
 */
#define SERVO_GPIO_PINS             {TUYA_GPIO_NUM_6, TUYA_GPIO_NUM_7}

/**
 * @brief 舵机PWM通道映射表 (用于兼容性)
 */
#define SERVO_PWM_CHANNELS          {TUYA_PWM_NUM_0, TUYA_PWM_NUM_1}

/**
 * @brief 舵机角度定义
 *
 * 根据用户需求：
 * - DP=1 (开门): 舵机转到90度位置
 * - DP=0 (关门): 舵机转到与开门相反方向，即0度位置
 */
#define SERVO_ANGLE_CLOSED          0           /**< 关闭位置角度 (与开门相反方向) */
#define SERVO_ANGLE_OPEN            90          /**< 开启位置角度 */

// ========== 枚举定义 ==========

/**
 * @brief 门状态枚举
 */
typedef enum {
    DOOR_STATE_CLOSED = 0,      /**< 门关闭 */
    DOOR_STATE_OPEN = 1,        /**< 门开启 */
    DOOR_STATE_UNKNOWN = 2      /**< 门状态未知 */
} door_state_e;

/**
 * @brief 连续旋转舵机状态枚举 - 基于占空比表格
 */
typedef enum {
    SERVO_STATE_STOP_0 = 0,     /**< 停止 - 0%占空比 */
    SERVO_STATE_CW_FAST = 1,    /**< 顺时钟快转 - 2.5%占空比 */
    SERVO_STATE_CW_SLOW = 2,    /**< 顺时钟慢转 - 5%占空比 */
    SERVO_STATE_STOP_75 = 3,    /**< 停止 - 7.5%占空比 */
    SERVO_STATE_CCW_SLOW = 4,   /**< 逆时针慢转 - 10%占空比 */
    SERVO_STATE_CCW_FAST = 5,   /**< 逆时针快转 - 12.5%占空比 */
    SERVO_STATE_STOP_100 = 6    /**< 停止 - 100%占空比 */
} servo_rotation_state_e;

/**
 * @brief 舵机ID枚举 - 智能药盒双门系统
 */
typedef enum {
    SERVO_ID_DOOR_1 = 0,        /**< 1号门舵机 (P06) */
    SERVO_ID_DOOR_2 = 1,        /**< 2号门舵机 (P07) */
    SERVO_ID_MAX = 2            /**< 舵机总数量 = 2 */
} servo_id_e;

// ========== 结构体定义 ==========

/**
 * @brief 舵机状态信息结构体
 */
typedef struct {
    servo_id_e servo_id;            /**< 舵机ID */
    uint8_t gpio_pin;               /**< GPIO引脚号 */
    uint16_t current_angle;         /**< 当前角度 (0-180) */
    uint16_t target_angle;          /**< 目标角度 (0-180) */
    door_state_e door_state;        /**< 门状态 */
    uint32_t last_operation_time;   /**< 最后操作时间 */
    uint32_t last_update_time;      /**< 最后更新时间 */
    uint32_t operation_count;       /**< 操作次数统计 */
    bool is_initialized;            /**< 是否已初始化 */
} servo_status_t;

/**
 * @brief PWM门控制系统状态结构体
 */
typedef struct {
    bool system_initialized;        /**< 系统是否已初始化 */
    servo_status_t servos[SERVO_ID_MAX]; /**< 舵机状态数组 */
    uint32_t total_operations;      /**< 总操作次数 */
    uint32_t init_time;             /**< 初始化时间 */
} pwm_door_system_t;

// ========== 函数声明 ==========

/**
 * @brief 初始化PWM门控制系统
 *
 * 初始化所有舵机PWM通道，设置初始位置为关闭状态
 *
 * @return OPERATE_RET 操作结果
 *         - OPRT_OK: 成功
 *         - 其他: 失败
 */
OPERATE_RET pwm_door_control_init(void);

/**
 * @brief MG90S舵机角度控制函数 - 参考STM32实现
 *
 * 基于MG90S控制逻辑，将角度转换为PWM占空比并控制舵机
 * 参考公式: temp = Angle*50/9 + 250
 *
 * @param[in] servo_id 舵机ID (SERVO_ID_DOOR_1 或 SERVO_ID_DOOR_2)
 * @param[in] angle 目标角度 (0-180度)
 *
 * @return OPERATE_RET 操作结果
 *         - OPRT_OK: 成功
 *         - 其他: 失败
 */
OPERATE_RET pwm_door_servo_angle_control(servo_id_e servo_id, uint8_t angle);

/**
 * @brief 连续旋转舵机状态控制函数 - 基于占空比表格
 *
 * 根据用户提供的占空比表格控制连续旋转舵机
 *
 * @param[in] servo_id 舵机ID (SERVO_ID_DOOR_1 或 SERVO_ID_DOOR_2)
 * @param[in] rotation_state 旋转状态 (servo_rotation_state_e)
 *
 * @return OPERATE_RET 操作结果
 *         - OPRT_OK: 成功
 *         - 其他: 失败
 */
OPERATE_RET pwm_door_servo_rotation_control(servo_id_e servo_id, servo_rotation_state_e rotation_state);

/**
 * @brief 门开启控制 - 使用连续旋转舵机
 *
 * 门开启：顺时钟慢转一定时间后停止
 *
 * @param[in] servo_id 舵机ID
 * @param[in] rotation_time_ms 旋转时间(毫秒)
 *
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_open_with_rotation(servo_id_e servo_id, uint32_t rotation_time_ms);

/**
 * @brief 门关闭控制 - 使用连续旋转舵机
 *
 * 门关闭：逆时针慢转一定时间后停止
 *
 * @param[in] servo_id 舵机ID
 * @param[in] rotation_time_ms 旋转时间(毫秒)
 *
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_close_with_rotation(servo_id_e servo_id, uint32_t rotation_time_ms);

/**
 * @brief 清理PWM门控制系统
 *
 * 停止所有PWM输出，释放资源
 */
void pwm_door_control_cleanup(void);

/**
 * @brief 控制指定舵机门的开关
 * 
 * @param servo_id 舵机ID
 * @param open 开关状态 (true=开启, false=关闭)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_control_set_state(servo_id_e servo_id, bool open);

/**
 * @brief 设置舵机到指定角度
 * 
 * @param servo_id 舵机ID
 * @param angle 目标角度 (0-180度)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_control_set_angle(servo_id_e servo_id, uint16_t angle);

/**
 * @brief 获取舵机状态信息
 * 
 * @param servo_id 舵机ID
 * @param status 状态信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_control_get_status(servo_id_e servo_id, servo_status_t *status);

/**
 * @brief 获取系统状态信息
 * 
 * @param system_status 系统状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_control_get_system_status(pwm_door_system_t *system_status);

/**
 * @brief 处理DP111门控制命令
 * 
 * @param open 开关状态 (true=开启, false=关闭)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_control_handle_dp111(bool open);

/**
 * @brief 处理DP112门控制命令
 *
 * @param open 开关状态 (true=开启, false=关闭)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_control_handle_dp112(bool open);

/**
 * @brief 门控制函数 - 统一接口
 *
 * @param servo_id 舵机ID
 * @param open 开关状态 (true=开启, false=关闭)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_control_door(servo_id_e servo_id, bool open);

/**
 * @brief 紧急停止所有舵机
 * 
 * 立即停止所有PWM输出，用于紧急情况
 */
void pwm_door_control_emergency_stop(void);

/**
 * @brief 运行PWM门控制系统测试
 *
 * 测试所有舵机的开关功能
 */
void pwm_door_control_run_tests(void);

/**
 * @brief 运行增强版PWM门控制系统测试 - 基于官方文档
 *
 * 测试所有增强功能：
 * - PWM配置信息获取
 * - 增强版角度控制
 * - 系统状态监控
 * - PWM信息动态设置
 */
void pwm_door_control_run_enhanced_tests(void);

// ========== MG90S舵机增强功能接口 - 基于Adafruit库优化 ==========

/**
 * @brief MG90S舵机平滑运动控制
 *
 * 实现平滑的舵机运动，避免突然的角度变化
 * 基于Adafruit PWM库的平滑控制算法
 *
 * @param servo_id 舵机ID (SERVO_ID_DOOR_1 或 SERVO_ID_DOOR_2)
 * @param target_angle 目标角度 (0-180度)
 * @param speed_ms 运动速度 (毫秒/度，建议10-50ms)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_servo_smooth_move(servo_id_e servo_id, uint16_t target_angle, uint32_t speed_ms);

/**
 * @brief MG90S舵机位置反馈检测
 *
 * 通过PWM信号分析估算舵机当前位置
 * 基于Adafruit库的反馈机制
 *
 * @param servo_id 舵机ID
 * @param current_angle 当前角度指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_servo_get_position(servo_id_e servo_id, uint16_t *current_angle);

/**
 * @brief MG90S舵机扭矩优化
 *
 * 根据负载情况自适应调整PWM参数
 * 基于Adafruit库的自适应控制思想
 *
 * @param servo_id 舵机ID
 * @param load_level 负载等级 (0-100，0=无负载，100=满负载)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_servo_optimize_torque(servo_id_e servo_id, uint8_t load_level);

/**
 * @brief MG90S舵机精确校准
 *
 * 对舵机进行精确校准，补偿个体差异
 * 基于Adafruit库的校准算法
 *
 * @param servo_id 舵机ID
 * @param angle 校准角度
 * @param actual_angle 实际测量角度
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_door_servo_calibrate(servo_id_e servo_id, uint16_t angle, uint16_t actual_angle);

/**
 * @brief 运行MG90S舵机增强功能测试
 *
 * 测试所有MG90S增强功能：
 * - 精确校准测试
 * - 平滑运动测试
 * - 位置反馈测试
 * - 扭矩优化测试
 */
void pwm_door_control_run_mg90s_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __PWM_DOOR_CONTROL_H__ */
